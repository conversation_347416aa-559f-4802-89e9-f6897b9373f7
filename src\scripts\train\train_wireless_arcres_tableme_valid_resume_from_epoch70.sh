python main.py ctdet \
--dataset table_labelmev2 \
--dataset_name TableLabelMe \
--data_config /aipdf-mlp/lanx/workspace/projects/LORE-adapt/src/lib/configs/my_dataset_all_wired_valid_configs.py \
--config_name tableme_full \
--exp_id train_tableme_all_wired_valid_resumeepoch70 \
--wiz_2dpe \
--wiz_stacking \
--tsfm_layers 4 \
--stacking_layers 4 \
--batch_size 26 \
--master_batch_size 10 \
--arch resfpnhalf_18 \
--lr 1e-4 \
--K 500 \
--MK 1000 -\
-num_epochs 130 \
--lr_step '30, 90' \
--gpus 6,7 \
--num_workers 16 \
--prefetch_factor 4 \
--val_intervals 3 \
--enable_data_cache \
--resume \
--load_model /aipdf-mlp/lanx/workspace/experiment_results/LORE/ctdet/train_tableme_all_valid_wired/model_last.pth \
--load_processor /aipdf-mlp/lanx/workspace/experiment_results/LORE/ctdet/train_tableme_all_valid_wired/processor_last.pth
#--cache_size 90000 \
#--image_cache_memory_mb 1000000
