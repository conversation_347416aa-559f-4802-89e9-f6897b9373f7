#!/usr/bin/env python3
"""
LORE-TSR数据加载性能分析工具

分析CTDetDataset.__getitem__()方法的详细耗时分布，
找出真正的性能瓶颈，为优化提供数据支持。
"""

import sys
import os
import time
import tempfile
import numpy as np
import argparse
from PIL import Image
from typing import Dict, List

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.timings = {}
        self.start_times = {}
    
    def start(self, name: str):
        """开始计时"""
        self.start_times[name] = time.time()
    
    def end(self, name: str):
        """结束计时"""
        if name in self.start_times:
            duration = time.time() - self.start_times[name]
            if name not in self.timings:
                self.timings[name] = []
            self.timings[name].append(duration)
            del self.start_times[name]
            return duration
        return 0
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        stats = {}
        total_time = 0
        
        for name, times in self.timings.items():
            avg_time = np.mean(times)
            total_time += avg_time
            stats[name] = {
                'avg_ms': avg_time * 1000,
                'total_ms': sum(times) * 1000,
                'count': len(times),
                'min_ms': min(times) * 1000,
                'max_ms': max(times) * 1000
            }
        
        # 计算百分比
        for name in stats:
            stats[name]['percentage'] = (stats[name]['avg_ms'] / (total_time * 1000)) * 100
        
        return stats

def create_test_images(count: int = 100) -> List[str]:
    """创建测试图像"""
    image_paths = []
    temp_dir = tempfile.mkdtemp(prefix='perf_analysis_')
    
    for i in range(count):
        # 创建1024x1024的测试图像（模拟实际大小）
        img_array = np.random.randint(0, 255, (1024, 1024, 3), dtype=np.uint8)
        img = Image.fromarray(img_array)
        
        img_path = os.path.join(temp_dir, f'test_image_{i:03d}.jpg')
        img.save(img_path, quality=95)
        image_paths.append(img_path)
    
    return image_paths, temp_dir

def analyze_getitem_performance(use_cache: bool = False, cache_size: int = 1000) -> Dict:
    """分析__getitem__方法的性能"""
    try:
        from lib.opts import opts
        from lib.datasets.sample.ctdet import CTDetDataset
        
        # 创建测试图像
        print(f"创建测试图像...")
        test_images, temp_dir = create_test_images(100)
        
        # 配置数据集
        if use_cache:
            opt = opts().parse(['ctdet', '--enable_data_cache', '--cache_size', str(cache_size), '--image_cache_memory_mb', str(cache_size * 3)])
            print(f"启用缓存: cache_size={cache_size}")
        else:
            opt = opts().parse(['ctdet'])
            print("禁用缓存")
        
        dataset = CTDetDataset()
        dataset.opt = opt
        
        # 模拟数据集结构
        dataset.images = [{'file_name': path, 'id': i} for i, path in enumerate(test_images)]
        dataset.num_samples = len(test_images)
        
        profiler = PerformanceProfiler()
        
        print(f"开始性能分析...")
        
        # 分析多个样本
        sample_count = 50
        for i in range(sample_count):
            img_path = test_images[i % len(test_images)]
            
            # 1. 图像加载
            profiler.start('image_loading')
            img = dataset._get_cached_image(img_path)
            profiler.end('image_loading')
            
            if img is None:
                continue
            
            # 2. 模拟数据增强（简化版本）
            profiler.start('data_augmentation')
            # 模拟仿射变换
            height, width = img.shape[:2]
            center = np.array([width / 2., height / 2.], dtype=np.float32)
            scale = 1.0
            
            # 模拟get_affine_transform和warpAffine
            import cv2
            M = cv2.getRotationMatrix2D((center[0], center[1]), 0, scale)
            img_aug = cv2.warpAffine(img, M, (width, height))
            profiler.end('data_augmentation')
            
            # 3. 模拟热力图生成
            profiler.start('heatmap_generation')
            # 模拟创建热力图
            output_h, output_w = 256, 256  # 典型的输出尺寸
            hm = np.zeros((1, output_h, output_w), dtype=np.float32)
            
            # 模拟draw_gaussian操作
            for _ in range(5):  # 假设5个对象
                x, y = np.random.randint(0, output_w), np.random.randint(0, output_h)
                radius = 10
                # 简化的高斯热力图生成
                gaussian = np.exp(-((np.arange(output_w) - x) ** 2 + (np.arange(output_h).reshape(-1, 1) - y) ** 2) / (2 * radius ** 2))
                hm[0] = np.maximum(hm[0], gaussian)
            profiler.end('heatmap_generation')
            
            # 4. 模拟其他处理
            profiler.start('other_processing')
            # 模拟数组转换、归一化等
            img_normalized = img_aug.astype(np.float32) / 255.0
            img_transposed = img_normalized.transpose(2, 0, 1)
            profiler.end('other_processing')
        
        # 清理
        import shutil
        shutil.rmtree(temp_dir)
        
        return profiler.get_stats()
        
    except Exception as e:
        print(f"性能分析失败: {e}")
        import traceback
        traceback.print_exc()
        return {}

def print_performance_report(stats_no_cache: Dict, stats_with_cache: Dict):
    """打印性能分析报告"""
    print("\n" + "="*60)
    print("LORE-TSR 数据加载性能分析报告")
    print("="*60)
    
    print(f"\n{'组件':<20} {'无缓存(ms)':<12} {'有缓存(ms)':<12} {'占比(%)':<10} {'提升(%)':<10}")
    print("-" * 60)
    
    total_improvement = 0
    total_time_no_cache = sum(stats_no_cache[k]['avg_ms'] for k in stats_no_cache)
    total_time_with_cache = sum(stats_with_cache[k]['avg_ms'] for k in stats_with_cache)
    
    for component in stats_no_cache:
        no_cache_time = stats_no_cache[component]['avg_ms']
        with_cache_time = stats_with_cache.get(component, {}).get('avg_ms', no_cache_time)
        percentage = stats_no_cache[component]['percentage']
        improvement = ((no_cache_time - with_cache_time) / no_cache_time * 100) if no_cache_time > 0 else 0
        
        print(f"{component:<20} {no_cache_time:<12.2f} {with_cache_time:<12.2f} {percentage:<10.1f} {improvement:<10.1f}")
    
    print("-" * 60)
    overall_improvement = ((total_time_no_cache - total_time_with_cache) / total_time_no_cache * 100) if total_time_no_cache > 0 else 0
    print(f"{'总计':<20} {total_time_no_cache:<12.2f} {total_time_with_cache:<12.2f} {'100.0':<10} {overall_improvement:<10.1f}")
    
    print(f"\n分析结论:")
    print(f"- 总体性能提升: {overall_improvement:.1f}%")
    print(f"- 主要瓶颈: {max(stats_no_cache, key=lambda x: stats_no_cache[x]['percentage'])}")
    
    # 给出优化建议
    print(f"\n优化建议:")
    if stats_no_cache['image_loading']['percentage'] > 30:
        print("- 图像加载是主要瓶颈，建议增大缓存大小")
    if stats_no_cache['data_augmentation']['percentage'] > 30:
        print("- 数据增强是主要瓶颈，建议使用Albumentations优化")
    if stats_no_cache['heatmap_generation']['percentage'] > 30:
        print("- 热力图生成是主要瓶颈，建议向量化优化")
    
    # 缓存大小建议
    if overall_improvement < 20:
        print(f"- 当前缓存效果有限，建议增大cache_size到5000-10000")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='LORE-TSR数据加载性能分析')
    parser.add_argument('--cache-size', type=int, default=1000, help='缓存大小')
    args = parser.parse_args()
    
    print("🔍 LORE-TSR 数据加载性能分析开始")
    
    # 分析无缓存性能
    print("\n1. 分析无缓存性能...")
    stats_no_cache = analyze_getitem_performance(use_cache=False)
    
    # 分析有缓存性能
    print(f"\n2. 分析有缓存性能 (cache_size={args.cache_size})...")
    stats_with_cache = analyze_getitem_performance(use_cache=True, cache_size=args.cache_size)
    
    if stats_no_cache and stats_with_cache:
        print_performance_report(stats_no_cache, stats_with_cache)
    else:
        print("❌ 性能分析失败")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
