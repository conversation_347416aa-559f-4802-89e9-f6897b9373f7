#!/usr/bin/env python3
"""
简单的图像加载替换测试脚本
验证CTDetDataset中的图像加载逻辑是否正确替换
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_image_loading_code_replacement():
    """测试图像加载代码是否正确替换"""
    try:
        print("=== 图像加载代码替换测试 ===")
        
        # 读取ctdet.py文件内容
        ctdet_file = os.path.join('lib', 'datasets', 'sample', 'ctdet.py')
        with open(ctdet_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含新的缓存加载代码
        assert 'self._get_cached_image(img_path)' in content, "缓存加载代码未找到"
        print("✅ 缓存加载代码存在")
        
        # 检查是否包含错误处理代码
        assert 'if img is None:' in content, "错误处理代码未找到"
        print("✅ 错误处理代码存在")
        
        # 检查是否移除了__getitem__方法中的原有torchvision.io代码
        lines = content.split('\n')
        # 查找__getitem__方法中的torchvision.io代码（排除_get_cached_image方法中的）
        in_getitem = False
        getitem_torchvision_lines = []
        for i, line in enumerate(lines):
            if 'def __getitem__(self' in line:
                in_getitem = True
            elif in_getitem and line.strip().startswith('def ') and '__getitem__' not in line:
                in_getitem = False
            elif in_getitem and 'tvio.read_image' in line and not line.strip().startswith('#'):
                getitem_torchvision_lines.append(line.strip())

        assert len(getitem_torchvision_lines) == 0, f"__getitem__方法中的原有torchvision.io代码未移除: {getitem_torchvision_lines}"
        print("✅ __getitem__方法中的原有torchvision.io代码已移除")
        
        # 检查导入是否正确
        assert 'from lib.utils.image_cache import ImageCache' in content, "ImageCache导入未找到"
        assert 'from lib.utils.logger_config import LoggerConfig' in content, "LoggerConfig导入未找到"
        print("✅ 必要的导入已添加")
        
        # 检查方法是否存在
        assert 'def _init_image_cache(self):' in content, "_init_image_cache方法未找到"
        assert 'def _get_cached_image(self, img_path):' in content, "_get_cached_image方法未找到"
        print("✅ 缓存方法已添加")
        
        print("\n🎉 图像加载代码替换测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 图像加载代码替换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_syntax_validity():
    """测试语法有效性"""
    try:
        print("\n=== 语法有效性测试 ===")
        
        # 尝试导入模块
        from lib.datasets.sample.ctdet import CTDetDataset
        print("✅ CTDetDataset模块导入成功")
        
        # 创建实例
        dataset = CTDetDataset()
        print("✅ CTDetDataset实例创建成功")
        
        # 检查方法存在
        assert hasattr(dataset, '_init_image_cache'), "_init_image_cache方法不存在"
        assert hasattr(dataset, '_get_cached_image'), "_get_cached_image方法不存在"
        print("✅ 缓存方法存在且可访问")
        
        print("\n🎉 语法有效性测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 语法有效性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success1 = test_image_loading_code_replacement()
    success2 = test_syntax_validity()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！图像加载逻辑替换成功！")
        sys.exit(0)
    else:
        print("\n❌ 测试失败！")
        sys.exit(1)
