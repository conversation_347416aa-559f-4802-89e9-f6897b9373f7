#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-28
# <AUTHOR> LORE-TSR Team
# @FileName: test_dataset_cache.py

"""
测试数据集索引缓存机制

用于验证任务1：预处理索引缓存机制实现的效果
"""

import os
import sys
import time
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from lib.opts import opts
from lib.datasets.dataset.table_labelmev2 import Table


def test_cache_performance():
    """测试缓存性能"""
    print("=== 数据集索引缓存性能测试 ===")
    
    # 创建测试配置
    opt = opts().init()
    opt.enable_data_cache = True  # 启用缓存
    opt.cache_size = 15000
    opt.save_dir = './test_cache'
    
    # 设置测试数据路径（使用实际的数据路径）
    if hasattr(opt, 'data_dir') and opt.data_dir:
        print(f"使用配置的数据目录: {opt.data_dir}")
    else:
        print("警告: 未配置数据目录，测试可能失败")
        return
    
    split = 'train'
    
    print(f"\n1. 第一次加载（应该进行完整的文件扫描和质量过滤）")
    start_time = time.time()
    
    try:
        dataset1 = Table(opt, split)
        first_load_time = time.time() - start_time
        
        print(f"✓ 第一次加载完成")
        print(f"  - 加载时间: {first_load_time:.2f}秒")
        print(f"  - 样本数量: {len(dataset1.images)}")
        print(f"  - 文件索引大小: {len(dataset1.file_index)}")
        
        # 显示缓存统计
        cache_stats = dataset1.dataset_cache.get_cache_stats()
        print(f"  - 缓存命中: {cache_stats.get('hits', 0)}")
        print(f"  - 缓存未命中: {cache_stats.get('misses', 0)}")
        
    except Exception as e:
        print(f"✗ 第一次加载失败: {e}")
        return
    
    print(f"\n2. 第二次加载（应该从缓存快速加载）")
    start_time = time.time()
    
    try:
        dataset2 = Table(opt, split)
        second_load_time = time.time() - start_time
        
        print(f"✓ 第二次加载完成")
        print(f"  - 加载时间: {second_load_time:.2f}秒")
        print(f"  - 样本数量: {len(dataset2.images)}")
        print(f"  - 文件索引大小: {len(dataset2.file_index)}")
        
        # 显示缓存统计
        cache_stats = dataset2.dataset_cache.get_cache_stats()
        print(f"  - 缓存命中: {cache_stats.get('hits', 0)}")
        print(f"  - 缓存未命中: {cache_stats.get('misses', 0)}")
        
        # 计算性能提升
        if first_load_time > 0:
            speedup = first_load_time / second_load_time
            time_saved = first_load_time - second_load_time
            print(f"\n性能提升分析:")
            print(f"  - 时间节省: {time_saved:.2f}秒")
            print(f"  - 速度提升: {speedup:.1f}倍")
            print(f"  - 性能提升: {(1 - second_load_time/first_load_time)*100:.1f}%")
            
            # 验证目标达成
            if second_load_time < 10:  # 目标：减少到5-10秒
                print(f"✓ 达成目标：第二次加载时间 {second_load_time:.2f}s < 10s")
            else:
                print(f"⚠ 未完全达成目标：第二次加载时间 {second_load_time:.2f}s >= 10s")
                
            if speedup >= 5:  # 期望至少5倍提升
                print(f"✓ 性能提升显著：{speedup:.1f}倍")
            else:
                print(f"⚠ 性能提升有限：{speedup:.1f}倍")
        
        # 验证数据一致性
        print(f"\n3. 数据一致性验证")
        if len(dataset1.images) == len(dataset2.images):
            print(f"✓ 样本数量一致: {len(dataset1.images)}")
        else:
            print(f"✗ 样本数量不一致: {len(dataset1.images)} vs {len(dataset2.images)}")
            
        if len(dataset1.file_index) == len(dataset2.file_index):
            print(f"✓ 文件索引大小一致: {len(dataset1.file_index)}")
        else:
            print(f"✗ 文件索引大小不一致: {len(dataset1.file_index)} vs {len(dataset2.file_index)}")
        
    except Exception as e:
        print(f"✗ 第二次加载失败: {e}")
        return
    
    print(f"\n=== 测试完成 ===")


def test_cache_invalidation():
    """测试缓存失效机制"""
    print("\n=== 缓存失效机制测试 ===")
    
    # 创建测试配置
    opt = opts().init()
    opt.enable_data_cache = True
    opt.save_dir = './test_cache'
    
    split = 'train'
    
    try:
        # 创建数据集实例
        dataset = Table(opt, split)
        
        # 获取缓存统计
        cache_stats = dataset.dataset_cache.get_cache_stats()
        print(f"缓存统计:")
        print(f"  - 缓存目录: {cache_stats.get('cache_dir', 'N/A')}")
        print(f"  - 缓存命中: {cache_stats.get('hits', 0)}")
        print(f"  - 缓存未命中: {cache_stats.get('misses', 0)}")
        print(f"  - 命中率: {cache_stats.get('hit_rate', 0):.1f}%")
        
        # 测试缓存清理
        print(f"\n清理缓存...")
        dataset.dataset_cache.clear_cache(split)
        
        print(f"✓ 缓存清理完成")
        
    except Exception as e:
        print(f"✗ 缓存失效测试失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试数据集索引缓存机制')
    parser.add_argument('--test-type', choices=['performance', 'invalidation', 'all'], 
                       default='all', help='测试类型')
    
    args = parser.parse_args()
    
    if args.test_type in ['performance', 'all']:
        test_cache_performance()
    
    if args.test_type in ['invalidation', 'all']:
        test_cache_invalidation()


if __name__ == '__main__':
    main()
