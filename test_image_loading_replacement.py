#!/usr/bin/env python3
"""
图像加载替换测试脚本
验证CTDetDataset中的图像加载逻辑是否正确替换为缓存版本
"""

import sys
import os
import tempfile
import numpy as np
from PIL import Image

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def create_test_image(width=256, height=256):
    """创建测试图像文件"""
    # 创建随机图像
    img_array = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
    img = Image.fromarray(img_array)
    
    # 保存到临时文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.jpg', delete=False)
    img.save(temp_file.name)
    return temp_file.name

def test_image_loading_replacement():
    """测试图像加载替换"""
    try:
        print("=== 图像加载替换测试 ===")
        
        # 创建测试图像
        test_img_path = create_test_image()
        print(f"创建测试图像: {test_img_path}")
        
        # 导入必要模块
        from lib.opts import opts
        from lib.datasets.sample.ctdet import CTDetDataset
        
        # 测试1: 缓存禁用模式的图像加载
        print("\n--- 测试1: 缓存禁用模式 ---")
        opt_disabled = opts().parse(['ctdet'])
        opt_disabled.enable_data_cache = False
        
        dataset_disabled = CTDetDataset()
        dataset_disabled.opt = opt_disabled
        
        # 直接测试_get_cached_image方法
        img1 = dataset_disabled._get_cached_image(test_img_path)
        assert img1 is not None, "缓存禁用模式图像加载失败"
        assert isinstance(img1, np.ndarray), f"图像类型错误: {type(img1)}"
        assert img1.dtype == np.uint8, f"图像数据类型错误: {img1.dtype}"
        assert len(img1.shape) == 3, f"图像维度错误: {img1.shape}"
        assert img1.shape[2] == 3, f"图像通道数错误: {img1.shape[2]}"
        print(f"✅ 缓存禁用模式正常 - 图像尺寸: {img1.shape}, 数据类型: {img1.dtype}")
        
        # 测试2: 缓存启用模式的图像加载
        print("\n--- 测试2: 缓存启用模式 ---")
        opt_enabled = opts().parse(['ctdet', '--enable_data_cache', '--cache_size', '100', '--image_cache_memory_mb', '256'])
        
        dataset_enabled = CTDetDataset()
        dataset_enabled.opt = opt_enabled
        
        # 第一次加载（缓存未命中）
        img2 = dataset_enabled._get_cached_image(test_img_path)
        assert img2 is not None, "缓存启用模式图像加载失败"
        assert isinstance(img2, np.ndarray), f"图像类型错误: {type(img2)}"
        assert img2.dtype == np.uint8, f"图像数据类型错误: {img2.dtype}"
        assert len(img2.shape) == 3, f"图像维度错误: {img2.shape}"
        assert img2.shape[2] == 3, f"图像通道数错误: {img2.shape[2]}"
        print(f"✅ 缓存启用模式正常 - 图像尺寸: {img2.shape}, 数据类型: {img2.dtype}")
        
        # 第二次加载（缓存命中）
        img3 = dataset_enabled._get_cached_image(test_img_path)
        assert img3 is not None, "缓存命中加载失败"
        assert np.array_equal(img2, img3), "缓存图像与原图像不一致"
        print("✅ 缓存命中测试通过")
        
        # 测试3: 图像格式一致性
        print("\n--- 测试3: 图像格式一致性 ---")
        assert img1.shape == img2.shape, f"不同模式图像尺寸不一致: {img1.shape} vs {img2.shape}"
        assert img1.dtype == img2.dtype, f"不同模式图像数据类型不一致: {img1.dtype} vs {img2.dtype}"
        print("✅ 图像格式一致性验证通过")
        
        # 测试4: 缓存统计
        print("\n--- 测试4: 缓存统计 ---")
        if hasattr(dataset_enabled, '_image_cache') and dataset_enabled._image_cache is not None:
            stats = dataset_enabled._image_cache.get_stats()
            print(f"缓存统计: 命中={stats['cache_hits']}, 未命中={stats['cache_misses']}, 命中率={stats['hit_rate_percent']}%")
            assert stats['cache_hits'] >= 1, "缓存命中次数应该大于等于1"
            assert stats['cache_misses'] >= 1, "缓存未命中次数应该大于等于1"
            print("✅ 缓存统计正常")
        
        # 清理测试文件
        os.unlink(test_img_path)
        
        print("\n🎉 所有图像加载替换测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 图像加载替换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_image_loading_replacement()
    sys.exit(0 if success else 1)
