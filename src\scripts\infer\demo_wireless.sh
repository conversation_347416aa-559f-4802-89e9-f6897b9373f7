python demo.py ctdet_mid \
        --dataset table_mid \
        --demo ../input_images/vis4tsr \
        --demo_name demo_wireless \
        --debug 1 \
        --arch resfpnhalf_18  \
        --K 3000 \
        --MK 5000 \
        --upper_left \
        --tsfm_layers 4\
        --stacking_layers 4 \
        --gpus 0\
        --wiz_2dpe \
        --wiz_detect \
        --wiz_stacking \
        --convert_onnx 0 \
        --vis_thresh_corner 0.3 \
        --vis_thresh 0.2 \
        --scores_thresh 0.2 \
        --nms \
        --demo_dir ../visualization_wireless_mine_vis4tsr/ \
        --load_model /aipdf-mlp/lanx/workspace/projects/LORE/src/ckpt_wireless/model_best.pth \
        --load_processor /aipdf-mlp/lanx/workspace/projects/LORE/src/ckpt_wireless/processor_best.pth