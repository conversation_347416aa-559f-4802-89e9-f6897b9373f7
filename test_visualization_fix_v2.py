#!/usr/bin/env python3
"""
测试可视化修复 v2 - 专门测试数据维度问题
"""

import sys
import os
import numpy as np
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root / "src"))

def create_realistic_test_data():
    """创建更真实的测试数据，模拟实际训练数据的维度"""
    
    # 基于错误信息，我们知道：
    # - hm_mask 维度是 300 (max_objs)
    # - 实际有效单元格数量变化很大 (6, 15, 20 等)
    
    input_h, input_w = 1024, 1024
    output_h, output_w = 256, 256
    max_objs = 300
    max_cors = 1000
    num_classes = 2
    
    # 模拟实际的有效单元格数量
    actual_valid_cells = np.random.randint(5, 25)  # 5-25个有效单元格
    actual_valid_corners = np.random.randint(20, 100)  # 20-100个有效角点
    
    print(f"🔧 Creating test data with {actual_valid_cells} valid cells, {actual_valid_corners} valid corners")
    
    ret_dict = {
        'input': np.random.randn(3, input_h, input_w).astype(np.float32),
        'hm': np.random.rand(num_classes, output_h, output_w).astype(np.float32) * 0.1,
        'hm_ind': np.zeros(max_objs, dtype=np.int64),
        'hm_mask': np.zeros(max_objs, dtype=np.uint8),
        'mk_ind': np.zeros(max_cors, dtype=np.int64),
        'mk_mask': np.zeros(max_cors, dtype=np.uint8),
        'reg': np.zeros((max_objs*5, 2), dtype=np.float32),
        'reg_ind': np.zeros(max_objs*5, dtype=np.int64),
        'reg_mask': np.zeros(max_objs*5, dtype=np.uint8),
        'wh': np.zeros((max_objs, 8), dtype=np.float32),
        'st': np.zeros((max_cors, 8), dtype=np.float32),
        'ctr_cro_ind': np.zeros(max_objs*4, dtype=np.int64),
        'cc_match': np.zeros((max_objs, 4), dtype=np.int64),
        'hm_ctxy': np.zeros((max_objs, 2), dtype=np.float32),
        'logic': np.zeros((max_objs, 4), dtype=np.float32),
        'h_pair_ind': np.zeros(100, dtype=np.int64),
        'v_pair_ind': np.zeros(100, dtype=np.int64)
    }
    
    # 设置有效的单元格数据
    for i in range(actual_valid_cells):
        # 设置有效掩码
        ret_dict['hm_mask'][i] = 1
        
        # 随机生成中心点位置
        center_x = np.random.uniform(50, output_w - 50)
        center_y = np.random.uniform(50, output_h - 50)
        ret_dict['hm_ctxy'][i] = [center_x, center_y]
        
        # 生成热力图索引
        ret_dict['hm_ind'][i] = int(center_y) * output_w + int(center_x)
        
        # 在热力图上添加高斯峰
        y_int, x_int = int(center_y), int(center_x)
        radius = 3
        for dy in range(-radius, radius+1):
            for dx in range(-radius, radius+1):
                y_pos = y_int + dy
                x_pos = x_int + dx
                if 0 <= y_pos < output_h and 0 <= x_pos < output_w:
                    dist = np.sqrt(dx*dx + dy*dy)
                    if dist <= radius:
                        value = np.exp(-(dist*dist) / (2 * (radius/3)**2))
                        ret_dict['hm'][0, y_pos, x_pos] = max(ret_dict['hm'][0, y_pos, x_pos], value)
        
        # 生成边界框（四个角点相对于中心的偏移）
        cell_w = np.random.uniform(15, 40)
        cell_h = np.random.uniform(10, 30)
        
        # 四个角点的偏移
        ret_dict['wh'][i] = [
            -cell_w/2, -cell_h/2,  # 左上角
            cell_w/2, -cell_h/2,   # 右上角
            cell_w/2, cell_h/2,    # 右下角
            -cell_w/2, cell_h/2    # 左下角
        ]
        
        # 生成逻辑坐标
        row = i // 5  # 假设每行5个单元格
        col = i % 5
        
        # 随机生成一些合并单元格
        if np.random.random() < 0.2:  # 20% 概率为合并单元格
            row_span = np.random.randint(1, 3)
            col_span = np.random.randint(1, 3)
            ret_dict['logic'][i] = [row, row + row_span - 1, col, col + col_span - 1]
        else:
            ret_dict['logic'][i] = [row, row, col, col]
        
        # 设置回归目标
        ret_dict['reg'][i] = [center_x - int(center_x), center_y - int(center_y)]
        ret_dict['reg_mask'][i] = 1
        ret_dict['reg_ind'][i] = ret_dict['hm_ind'][i]
    
    # 设置有效的角点数据
    for i in range(actual_valid_corners):
        ret_dict['mk_mask'][i] = 1
        ret_dict['mk_ind'][i] = np.random.randint(0, output_h * output_w)
    
    # 设置角点匹配关系（简化版本）
    for i in range(actual_valid_cells):
        # 为每个单元格分配4个角点索引
        corner_start = i * 4
        if corner_start + 3 < actual_valid_corners:
            ret_dict['cc_match'][i] = [corner_start, corner_start+1, corner_start+2, corner_start+3]
        else:
            # 如果角点不够，使用随机索引
            ret_dict['cc_match'][i] = np.random.randint(0, min(actual_valid_corners, 4), 4)
    
    return ret_dict


def test_visualization_with_real_data():
    """使用真实数据测试可视化"""
    print("🧪 Testing visualization with realistic data...")
    
    try:
        from lib.utils.data_visualizer import debug_ret_dict_structure, visualize_training_sample
        
        # 创建模拟的 opt 对象
        class MockOpt:
            def __init__(self):
                self.enable_visualization = True
                self.visualization_dir = "test_debug_vis_v2"
                self.visualization_sample_rate = 1.0
                self.vis_max_samples_per_epoch = 100
                self.down_ratio = 4
        
        opt = MockOpt()
        
        # 创建真实的测试数据
        ret_dict = create_realistic_test_data()
        
        # 调试数据结构
        debug_ret_dict_structure(ret_dict, 0)
        
        # 测试可视化
        print(f"\n🎨 Testing visualization...")
        visualize_training_sample(
            ret_dict=ret_dict,
            sample_id="test_realistic_001",
            save_dir="test_debug_vis_v2",
            down_ratio=4
        )
        
        print("✅ Visualization test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🔧 LORE-TSR Visualization Fix v2 Test")
    print("=" * 60)
    
    # 检查依赖
    try:
        import matplotlib
        import cv2
        import numpy as np
        print("✅ Dependencies check passed")
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        return
    
    # 运行测试
    if test_visualization_with_real_data():
        print("\n🎉 Test passed! Check the generated files:")
        print("   - test_debug_vis_v2/")
        print("\n💡 Key fixes applied:")
        print("1. Fixed logic coordinate indexing bug")
        print("2. Added comprehensive error handling")
        print("3. Changed all titles to English")
        print("4. Added debug information for troubleshooting")
        print("5. Fixed combined and corners visualization")
    else:
        print("\n⚠️ Test failed, please check error messages above")
    
    # 清理测试文件
    import shutil
    test_dir = Path("test_debug_vis_v2")
    if test_dir.exists():
        print(f"\n🧹 Cleaning up test directory: {test_dir}")
        # shutil.rmtree(test_dir)  # 注释掉以便检查结果


if __name__ == "__main__":
    main()
