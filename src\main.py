from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import _init_paths

import os

import torch
import torch.utils.data
from opts import opts
from models.model import create_model, load_model, save_model
from models.data_parallel import DataParallel
from logger import Logger
# 修改这一行，使用绝对导入
from lib.datasets.dataset_factory import get_dataset
from trains.train_factory import train_factory
from models.classifier import Processor
from lib.utils.data_visualizer import setup_ctdet_visualization


def get_tableme_dataset(opt):
  """
  获取TableLabelMe数据集类（步骤5.5简化版本）。

  利用步骤5.4的智能工厂函数，大大简化数据集创建逻辑。

  Args:
      opt: 命令行参数对象，包含TableLabelMe配置信息

  Returns:
      class: TableLabelMe数据集类

  Note:
      这是步骤5.5的简化版本，利用智能工厂函数实现统一的数据集创建流程。
  """
  print("[信息] 使用智能工厂函数初始化TableLabelMe数据集...")

  # 验证TableLabelMe配置
  if not hasattr(opt, 'dataset_mode') or opt.dataset_mode != 'TableLabelMe':
    raise ValueError("TableLabelMe模式需要正确的dataset_mode配置")

  # 构建config对象（步骤5.4智能工厂函数要求的格式）
  config_data = {
    'dataset_mode': 'TableLabelMe',
    'description': getattr(opt, 'config_data', {}).get('description', 'TableLabelMe数据集配置'),
    'data_paths': getattr(opt, 'data_paths', {}),
    'unified_config': getattr(opt, 'unified_config', {})
  }

  # 输出配置信息
  data_paths = config_data.get('data_paths', {})
  if data_paths:
    train_paths = data_paths.get('train', [])
    val_paths = data_paths.get('val', [])
    print(f"[信息] TableLabelMe数据路径 - 训练: {len(train_paths)}个, 验证: {len(val_paths)}个")
  else:
    print("[信息] 使用TableLabelMe默认数据路径")

  # 使用步骤5.4的智能工厂函数创建数据集类
  Dataset = get_dataset(opt.dataset, opt.task, config_data)

  print(f"[信息] TableLabelMe数据集初始化完成 - 类名: {Dataset.__name__}")
  return Dataset


def main(opt):
  torch.autograd.set_detect_anomaly(True)
  torch.manual_seed(opt.seed)
  torch.backends.cudnn.benchmark = not opt.not_cuda_benchmark and not opt.test

  # 设置可视化功能（在数据集创建之前）
  print(f"🎨 设置可视化功能...")
  setup_ctdet_visualization(opt)

  # 步骤5.5：统一的数据集创建流程（利用智能工厂函数）
  dataset_mode = getattr(opt, 'dataset_mode', 'COCO')
  print(f"[信息] 检测到数据集模式: {dataset_mode}")

  # 构建config对象用于智能工厂函数
  if dataset_mode == 'TableLabelMe':
    # TableLabelMe模式：构建完整的config对象
    config_data = {
      'dataset_mode': 'TableLabelMe',
      'description': getattr(opt, 'config_data', {}).get('description', 'TableLabelMe数据集配置'),
      'data_paths': getattr(opt, 'data_paths', {}),
      'unified_config': getattr(opt, 'unified_config', {})
    }
    Dataset = get_dataset(opt.dataset, opt.task, config_data)
    print(f"[信息] 使用智能工厂函数创建TableLabelMe数据集: {Dataset.__name__}")
  else:
    # COCO模式：可以使用新方式或保持向后兼容
    config_data = {'dataset_mode': 'COCO'}
    Dataset = get_dataset(opt.dataset, opt.task, config_data)
    print(f"[信息] 使用智能工厂函数创建COCO数据集: {Dataset.__name__}")

  # 更新数据集信息和设置头部
  opt = opts().update_dataset_info_and_set_heads(opt, Dataset)

  # 输出数据集配置信息（步骤5.5增强版本）
  print(f"[信息] 数据集配置完成 - 类别数: {opt.num_classes}, 分辨率: {opt.input_h}x{opt.input_w}")
  print(f"[信息] 数据集类名: {Dataset.__name__}")

  if dataset_mode == 'TableLabelMe':
    # 输出TableLabelMe特有的配置信息
    config_data = getattr(opt, 'config_data', None)
    if config_data:
      print(f"[信息] TableLabelMe配置: {config_data.get('description', '无描述')}")

    unified_config = getattr(opt, 'unified_config', None)
    if unified_config:
      metadata = unified_config.get('config_metadata', {})
      print(f"[信息] 配置状态: {metadata.get('validation_status', '未知')}")

    data_paths = getattr(opt, 'data_paths', {})
    if data_paths:
      train_count = len(data_paths.get('train', []))
      val_count = len(data_paths.get('val', []))
      print(f"[信息] 数据路径配置: 训练{train_count}个, 验证{val_count}个")

  print(f"[信息] 步骤5.5统一数据集创建流程完成 - 模式: {dataset_mode}")
  #print(opt)

  logger = Logger(opt)

  os.environ['CUDA_VISIBLE_DEVICES'] = opt.gpus_str
  opt.device = torch.device('cuda' if opt.gpus[0] >= 0 else 'cpu')
  
  model = create_model(opt.arch, opt.heads, opt.head_conv)
  Trainer = train_factory[opt.task]
 

  processor = Processor(opt)
  processor.train()
  optimizer = torch.optim.Adam([  \
              {'params': model.parameters()}, \
              {'params': processor.parameters()}],  lr =opt.lr, betas= (0.9, 0.98), eps=1e-9)

  trainer = Trainer(opt, model, optimizer, processor)



  start_epoch = 0
  if opt.load_model != '':
    #model, optimizer, start_epoch = load_model(model, opt.load_model)
      #model, opt.load_model, optimizer, opt.resume, opt.lr, opt.lr_step)
    model = load_model(model, opt.load_model)

  if opt.load_processor != '':
    processor = load_model(processor, opt.load_processor)
  
  trainer.set_device(opt.gpus, opt.chunk_sizes, opt.device)

  # 创建验证数据加载器（Bug修复 - 使用调试collate函数）
  print(f"[主程序] 🔧 创建验证数据加载器（调试版本）")
  try:
    # 导入调试collate函数
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from debug_collate import debug_collate_fn

    val_dataset = Dataset(opt, 'val')
    print(f"[主程序] ✅ 验证数据集创建成功: {len(val_dataset)}个样本")

    val_loader = torch.utils.data.DataLoader(
        val_dataset,
        batch_size=1,
        shuffle=False,
        num_workers=0,  # 禁用多进程以便调试
        pin_memory=False,  # 禁用pin_memory以简化调试
        # collate_fn=debug_collate_fn  # 使用调试collate函数
    )
    print(f"[主程序] ✅ 验证数据加载器创建成功（使用调试collate函数）")
  except Exception as e:
    print(f"[主程序] ❌ 验证数据加载器创建失败: {e}")
    import traceback
    traceback.print_exc()
    raise

  if opt.test:
    _, preds = trainer.val(0, val_loader)
    val_loader.dataset.run_eval(preds, opt.save_dir)
    return

  # 创建训练数据加载器（Bug修复 - 禁用多进程调试）
  print(f"[主程序] 🔧 创建训练数据加载器")
  try:
    train_dataset = Dataset(opt, 'train')
    print(f"[主程序] ✅ 训练数据集创建成功: {len(train_dataset)}个样本")

    train_loader = torch.utils.data.DataLoader(
        train_dataset,
        batch_size=opt.batch_size,
        shuffle=True,
        # num_workers=0,  # 临时禁用多进程以便调试        FUCK YOU! AUGMENT AI
        # pin_memory=False,  # 禁用pin_memory以简化调试  FUCK YOU! AUGMENT AI
        num_workers=min(opt.num_workers, 16),
        pin_memory=True,
        # prefetch_factor=min(opt.prefetch_factor, 4),
        prefetch_factor=max(4, opt.batch_size // 2),
        drop_last=True,
        persistent_workers=True  # 保持worker进程，减少启动开销
    )
    print(f"[主程序] ✅ 训练数据加载器创建成功")
  except Exception as e:
    print(f"[主程序] ❌ 训练数据加载器创建失败: {e}")
    import traceback
    traceback.print_exc()
    raise

  # 步骤5.5：端到端训练流程集成验证
  print(f"[信息] 步骤5.5端到端训练流程集成验证...")
  print(f"[信息] 数据集模式: {dataset_mode}")
  print(f"[信息] 数据集类名: {Dataset.__name__}")
  print(f"[信息] 训练样本数: {len(train_loader.dataset)}")
  print(f"[信息] 验证样本数: {len(val_loader.dataset)}")
  print(f"[信息] 批次大小: {opt.batch_size}")
  print(f"[信息] 训练轮数: {opt.num_epochs}")
  print(f"[信息] 工作进程数: {opt.num_workers}")

  # 验证数据集兼容性（Bug修复 - 增强测试）
  print(f"[主程序] 🧪 开始数据加载兼容性测试（Bug修复版本）")

  # 测试训练数据加载
  try:
    print(f"[主程序] 🧪 测试训练数据加载...")
    sample_batch = next(iter(train_loader))
    print(f"[主程序] ✅ 训练数据加载测试成功")
    print(f"[主程序] 📊 批次信息: input_shape={sample_batch['input'].shape if 'input' in sample_batch else '未知'}")
    if 'logic' in sample_batch:
      logic_tensor = sample_batch['logic']
      print(f"[主程序] 📊 logic张量: shape={logic_tensor.shape}, dtype={logic_tensor.dtype}, "
            f"range=[{logic_tensor.min():.2f}, {logic_tensor.max():.2f}]")
  except Exception as e:
    print(f"[主程序] ❌ 训练数据加载测试失败: {e}")
    import traceback
    traceback.print_exc()

  # 测试验证数据加载（重点测试）
  try:
    print(f"[主程序] 🧪 测试验证数据加载（重点测试）...")
    val_sample = next(iter(val_loader))
    print(f"[主程序] ✅ 验证数据加载测试成功")
    print(f"[主程序] 📊 验证批次信息: input_shape={val_sample['input'].shape if 'input' in val_sample else '未知'}")
    if 'logic' in val_sample:
      val_logic_tensor = val_sample['logic']
      print(f"[主程序] 📊 验证logic张量: shape={val_logic_tensor.shape}, dtype={val_logic_tensor.dtype}, "
            f"range=[{val_logic_tensor.min():.2f}, {val_logic_tensor.max():.2f}]")
  except Exception as e:
    print(f"[主程序] ❌ 验证数据加载测试失败: {e}")
    print(f"[主程序] 🚨 这可能是导致验证阶段错误的原因！")
    import traceback
    traceback.print_exc()

  print(f"[信息] {dataset_mode}模式训练流程集成完成")
  print('Starting training...')
  best = 1e10

  for epoch in range(start_epoch + 1, opt.num_epochs + 1):
    mark = epoch if opt.save_all else 'last'
    log_dict_train, _ = trainer.train(epoch, train_loader)

    logger.write('epoch: {} |'.format(epoch))
    for k, v in log_dict_train.items():
      logger.scalar_summary('train_{}'.format(k), v, epoch)
      logger.write('{} {:8f} | '.format(k, v))
   
    if opt.val_intervals > 0 and epoch % opt.val_intervals == 0:
      save_model(os.path.join(opt.save_dir, 'processor_{}.pth'.format(mark)), 
                epoch, processor, optimizer)
      save_model(os.path.join(opt.save_dir, 'model_{}.pth'.format(mark)), 
                epoch, model, optimizer)
      with torch.no_grad():
        log_dict_val, preds = trainer.val(epoch, val_loader)
      for k, v in log_dict_val.items():
        logger.scalar_summary('val_{}'.format(k), v, epoch)
        logger.write('{} {:8f} | '.format(k, v))

      if log_dict_val[opt.metric] < best:
        best = log_dict_val[opt.metric]
        save_model(os.path.join(opt.save_dir, 'processor_best.pth'), 
                  epoch, processor)
        save_model(os.path.join(opt.save_dir, 'model_best.pth'), 
                  epoch, model)
    else:
      save_model(os.path.join(opt.save_dir, 'processor_last.pth'), 
                epoch, processor, optimizer)
      save_model(os.path.join(opt.save_dir, 'model_last.pth'), 
                epoch, model, optimizer)
    logger.write('\n')
    if epoch in opt.lr_step:
      save_model(os.path.join(opt.save_dir, 'processor_{}.pth'.format(epoch)), 
                epoch, processor, optimizer)
      save_model(os.path.join(opt.save_dir, 'model_{}.pth'.format(epoch)), 
                epoch, model, optimizer)
      lr = opt.lr * (0.1 ** (opt.lr_step.index(epoch) + 1))
      print('Drop LR to', lr)
      for param_group in optimizer.param_groups:
          param_group['lr'] = lr

    
  logger.close()

if __name__ == '__main__':
  opt = opts().parse()
  main(opt)
