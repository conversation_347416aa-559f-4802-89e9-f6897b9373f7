#!/usr/bin/env python3
"""
CTDetDataset 可视化功能补丁脚本
自动为 ctdet.py 添加可视化功能
"""

import os
import sys
from pathlib import Path


def apply_ctdet_patch():
    """为 ctdet.py 应用可视化补丁"""
    
    # 定位 ctdet.py 文件
    ctdet_path = Path("src/lib/datasets/sample/ctdet.py")
    
    if not ctdet_path.exists():
        print(f"❌ 找不到文件: {ctdet_path}")
        print("请确保在项目根目录运行此脚本")
        return False
    
    print(f"📁 找到文件: {ctdet_path}")
    
    # 读取原文件
    with open(ctdet_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经应用过补丁
    if 'visualize_if_enabled' in content:
        print("✅ 可视化功能已经集成，无需重复应用补丁")
        return True
    
    # 应用补丁
    print("🔧 应用可视化补丁...")
    
    # 1. 添加导入
    import_line = "from lib.utils.data_visualizer import visualize_if_enabled"
    
    # 在现有导入后添加
    if "from lib.utils.logger_config import LoggerConfig" in content:
        content = content.replace(
            "from lib.utils.logger_config import LoggerConfig",
            f"from lib.utils.logger_config import LoggerConfig\n{import_line}"
        )
    else:
        # 如果找不到特定导入，在类定义前添加
        content = content.replace(
            "class CTDetDataset(data.Dataset):",
            f"{import_line}\n\nclass CTDetDataset(data.Dataset):"
        )
    
    # 2. 在 return ret 之前添加可视化调用
    # 查找 return ret 的位置
    return_pattern = "    return ret"
    
    if return_pattern in content:
        # 在 return ret 之前添加可视化调用
        visualization_code = """    # 可视化功能（自动添加）
    visualize_if_enabled(ret, index, self.opt)
    
"""
        content = content.replace(
            return_pattern,
            f"{visualization_code}{return_pattern}"
        )
    else:
        print("⚠️ 找不到 'return ret' 语句，请手动添加可视化调用")
        return False
    
    # 3. 备份原文件
    backup_path = ctdet_path.with_suffix('.py.backup')
    with open(backup_path, 'w', encoding='utf-8') as f:
        with open(ctdet_path, 'r', encoding='utf-8') as original:
            f.write(original.read())
    print(f"💾 原文件已备份至: {backup_path}")
    
    # 4. 写入修改后的文件
    with open(ctdet_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 可视化补丁应用成功！")
    return True


def apply_opts_patch():
    """为 opts.py 添加可视化参数"""
    
    opts_path = Path("src/lib/opts.py")
    
    if not opts_path.exists():
        print(f"❌ 找不到文件: {opts_path}")
        return False
    
    print(f"📁 找到文件: {opts_path}")
    
    # 读取原文件
    with open(opts_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经应用过补丁
    if 'add_visualization_args' in content:
        print("✅ 可视化参数已经集成")
        return True
    
    print("🔧 添加可视化参数...")
    
    # 1. 添加导入
    import_line = "from lib.utils.visualization_opts import add_visualization_args"
    
    # 在文件开头添加导入
    if "from __future__ import" in content:
        # 在 future imports 后添加
        lines = content.split('\n')
        insert_index = 0
        for i, line in enumerate(lines):
            if line.startswith('from __future__'):
                insert_index = i + 1
        
        lines.insert(insert_index + 1, import_line)
        content = '\n'.join(lines)
    
    # 2. 在 __init__ 方法中添加参数
    init_pattern = "    self.parser = argparse.ArgumentParser()"
    
    if init_pattern in content:
        add_args_code = f"""    self.parser = argparse.ArgumentParser()
    
    # 添加可视化参数
    add_visualization_args(self.parser)"""
        
        content = content.replace(init_pattern, add_args_code)
    else:
        print("⚠️ 找不到 ArgumentParser 初始化，请手动添加可视化参数")
        return False
    
    # 3. 备份并写入
    backup_path = opts_path.with_suffix('.py.backup')
    if not backup_path.exists():
        with open(backup_path, 'w', encoding='utf-8') as f:
            with open(opts_path, 'r', encoding='utf-8') as original:
                f.write(original.read())
        print(f"💾 opts.py 已备份至: {backup_path}")
    
    with open(opts_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 可视化参数添加成功！")
    return True


def apply_main_patch():
    """为 main.py 添加可视化设置"""
    
    main_path = Path("src/main.py")
    
    if not main_path.exists():
        print(f"❌ 找不到文件: {main_path}")
        return False
    
    print(f"📁 找到文件: {main_path}")
    
    # 读取原文件
    with open(main_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经应用过补丁
    if 'setup_ctdet_visualization' in content:
        print("✅ 主脚本可视化设置已经集成")
        return True
    
    print("🔧 添加可视化设置...")
    
    # 1. 添加导入
    import_line = "from lib.utils.data_visualizer import setup_ctdet_visualization"
    
    # 在现有导入后添加
    if "from lib.datasets.dataset_factory import get_dataset" in content:
        content = content.replace(
            "from lib.datasets.dataset_factory import get_dataset",
            f"from lib.datasets.dataset_factory import get_dataset\n{import_line}"
        )
    
    # 2. 在数据集创建前添加可视化设置
    dataset_pattern = "Dataset = get_dataset(opt.dataset, opt.task"
    
    if dataset_pattern in content:
        setup_code = """  # 设置可视化功能
  setup_ctdet_visualization(opt)
  
  """
        content = content.replace(
            f"  {dataset_pattern}",
            f"{setup_code}  {dataset_pattern}"
        )
    
    # 3. 备份并写入
    backup_path = main_path.with_suffix('.py.backup')
    if not backup_path.exists():
        with open(backup_path, 'w', encoding='utf-8') as f:
            with open(main_path, 'r', encoding='utf-8') as original:
                f.write(original.read())
        print(f"💾 main.py 已备份至: {backup_path}")
    
    with open(main_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 主脚本可视化设置添加成功！")
    return True


def verify_files():
    """验证必要的文件是否存在"""
    required_files = [
        "src/lib/utils/data_visualizer.py",
        "src/lib/utils/visualization_opts.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要的可视化文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        print("\n请先确保可视化模块文件已正确放置")
        return False
    
    print("✅ 所有必要的可视化文件都存在")
    return True


def main():
    """主函数"""
    print("🎨 LORE-TSR CTDetDataset 可视化功能自动集成")
    print("=" * 60)
    
    # 检查必要文件
    if not verify_files():
        return
    
    # 应用补丁
    success_count = 0
    total_patches = 3
    
    if apply_ctdet_patch():
        success_count += 1
    
    if apply_opts_patch():
        success_count += 1
    
    if apply_main_patch():
        success_count += 1
    
    # 总结
    print("\n" + "=" * 60)
    print(f"📊 补丁应用结果: {success_count}/{total_patches} 成功")
    
    if success_count == total_patches:
        print("🎉 所有补丁应用成功！")
        print("\n📝 使用方法:")
        print("python main.py ctdet --enable_visualization --visualization_sample_rate 0.1")
        print("\n📁 可视化结果将保存在 debug_visualizations/ 目录")
    else:
        print("⚠️ 部分补丁应用失败，请检查上述错误信息")
        print("💡 您也可以手动按照 CTDET_VISUALIZATION_INTEGRATION.md 进行修改")


if __name__ == "__main__":
    main()
