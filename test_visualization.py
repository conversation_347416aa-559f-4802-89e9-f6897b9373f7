#!/usr/bin/env python3
"""
LORE-TSR 数据可视化功能测试脚本
用于验证可视化功能是否正常工作
"""

import os
import sys
import numpy as np
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root / "src"))

try:
    from lib.utils.data_visualizer import LORETSRDataVisualizer, visualize_training_sample
    from lib.utils.visualization_opts import get_visualization_config, validate_visualization_config
    print("✅ 成功导入可视化模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保文件路径正确，并且所有依赖已安装")
    sys.exit(1)


def create_test_data():
    """创建测试数据"""
    print("🔧 创建测试数据...")
    
    # 模拟真实的训练数据维度
    input_h, input_w = 1024, 1024
    output_h, output_w = 256, 256
    max_objs = 300
    max_cors = 1000
    num_classes = 2
    
    # 创建更真实的测试数据
    ret_dict = {
        'input': np.random.randn(3, input_h, input_w).astype(np.float32),
        'hm': np.zeros((num_classes, output_h, output_w), dtype=np.float32),
        'hm_ind': np.zeros(max_objs, dtype=np.int64),
        'hm_mask': np.zeros(max_objs, dtype=np.uint8),
        'mk_ind': np.zeros(max_cors, dtype=np.int64),
        'mk_mask': np.zeros(max_cors, dtype=np.uint8),
        'reg': np.zeros((max_objs*5, 2), dtype=np.float32),
        'reg_ind': np.zeros(max_objs*5, dtype=np.int64),
        'reg_mask': np.zeros(max_objs*5, dtype=np.uint8),
        'wh': np.zeros((max_objs, 8), dtype=np.float32),
        'st': np.zeros((max_cors, 8), dtype=np.float32),
        'ctr_cro_ind': np.zeros(max_objs*4, dtype=np.int64),
        'cc_match': np.zeros((max_objs, 4), dtype=np.int64),
        'hm_ctxy': np.zeros((max_objs, 2), dtype=np.float32),
        'logic': np.zeros((max_objs, 4), dtype=np.float32),
        'h_pair_ind': np.zeros(100, dtype=np.int64),
        'v_pair_ind': np.zeros(100, dtype=np.int64)
    }
    
    # 创建一些真实的单元格数据
    num_cells = 15  # 模拟15个单元格
    
    for i in range(num_cells):
        # 设置有效掩码
        ret_dict['hm_mask'][i] = 1
        
        # 随机生成中心点位置
        center_x = np.random.uniform(50, output_w - 50)
        center_y = np.random.uniform(50, output_h - 50)
        ret_dict['hm_ctxy'][i] = [center_x, center_y]
        
        # 生成热力图索引
        ret_dict['hm_ind'][i] = int(center_y) * output_w + int(center_x)
        
        # 在热力图上添加高斯峰
        y_int, x_int = int(center_y), int(center_x)
        radius = 5
        for dy in range(-radius, radius+1):
            for dx in range(-radius, radius+1):
                y_pos = y_int + dy
                x_pos = x_int + dx
                if 0 <= y_pos < output_h and 0 <= x_pos < output_w:
                    dist = np.sqrt(dx*dx + dy*dy)
                    if dist <= radius:
                        value = np.exp(-(dist*dist) / (2 * (radius/3)**2))
                        ret_dict['hm'][0, y_pos, x_pos] = max(ret_dict['hm'][0, y_pos, x_pos], value)
        
        # 生成边界框（四个角点相对于中心的偏移）
        cell_w = np.random.uniform(20, 60)
        cell_h = np.random.uniform(15, 40)
        
        # 四个角点的偏移
        ret_dict['wh'][i] = [
            -cell_w/2, -cell_h/2,  # 左上角
            cell_w/2, -cell_h/2,   # 右上角
            cell_w/2, cell_h/2,    # 右下角
            -cell_w/2, cell_h/2    # 左下角
        ]
        
        # 生成逻辑坐标
        row = i // 5  # 假设每行5个单元格
        col = i % 5
        
        # 随机生成一些合并单元格
        if np.random.random() < 0.2:  # 20% 概率为合并单元格
            row_span = np.random.randint(1, 3)
            col_span = np.random.randint(1, 3)
            ret_dict['logic'][i] = [row, row + row_span - 1, col, col + col_span - 1]
        else:
            ret_dict['logic'][i] = [row, row, col, col]
        
        # 设置回归目标
        ret_dict['reg'][i] = [center_x - int(center_x), center_y - int(center_y)]
        ret_dict['reg_mask'][i] = 1
        ret_dict['reg_ind'][i] = ret_dict['hm_ind'][i]
    
    print(f"✅ 创建了 {num_cells} 个测试单元格")
    return ret_dict


def test_basic_visualization():
    """测试基础可视化功能"""
    print("\n🧪 测试基础可视化功能...")
    
    # 创建测试数据
    ret_dict = create_test_data()
    
    # 创建可视化器
    save_dir = "test_visualizations"
    visualizer = LORETSRDataVisualizer(save_dir=save_dir)
    
    try:
        # 执行可视化
        visualizer.visualize_and_save(ret_dict, "test_sample_001", down_ratio=4)
        print("✅ 基础可视化测试通过")
        
        # 检查生成的文件
        expected_files = [
            "input/test_sample_001_input.jpg",
            "heatmap/test_sample_001_heatmaps.png",
            "bbox/test_sample_001_bbox.png",
            "logic/test_sample_001_logic.png",
            "logic/test_sample_001_logic_table.json",
            "corners/test_sample_001_corners.png",
            "combined/test_sample_001_combined.png",
            "test_sample_001_statistics.json"
        ]
        
        missing_files = []
        for file_path in expected_files:
            full_path = os.path.join(save_dir, file_path)
            if not os.path.exists(full_path):
                missing_files.append(file_path)
        
        if missing_files:
            print(f"⚠️ 以下文件未生成: {missing_files}")
        else:
            print("✅ 所有预期文件都已生成")
            
    except Exception as e:
        print(f"❌ 可视化测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_convenience_function():
    """测试便捷函数"""
    print("\n🧪 测试便捷函数...")
    
    ret_dict = create_test_data()
    
    try:
        visualize_training_sample(
            ret_dict=ret_dict,
            sample_id="test_convenience_001",
            save_dir="test_convenience_vis",
            down_ratio=4
        )
        print("✅ 便捷函数测试通过")
    except Exception as e:
        print(f"❌ 便捷函数测试失败: {e}")


def test_configuration():
    """测试配置功能"""
    print("\n🧪 测试配置功能...")
    
    # 模拟配置对象
    class MockOpt:
        def __init__(self):
            self.enable_visualization = True
            self.visualization_dir = "test_config_vis"
            self.visualization_sample_rate = 0.1
            self.visualization_level = 2
            self.down_ratio = 4
    
    opt = MockOpt()
    
    try:
        vis_config = get_visualization_config(opt)
        is_valid, errors = validate_visualization_config(vis_config)
        
        if is_valid:
            print("✅ 配置验证通过")
        else:
            print(f"❌ 配置验证失败: {errors}")
            
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")


def main():
    """主测试函数"""
    print("🎨 LORE-TSR 数据可视化功能测试")
    print("=" * 60)
    
    # 检查依赖
    try:
        import matplotlib
        import cv2
        import seaborn
        print("✅ 所有依赖库已安装")
    except ImportError as e:
        print(f"❌ 缺少依赖库: {e}")
        print("请安装: pip install matplotlib opencv-python seaborn")
        return
    
    # 运行测试
    test_configuration()
    test_basic_visualization()
    test_convenience_function()
    
    print("\n🎉 测试完成！")
    print("📁 请检查生成的可视化文件:")
    print("   - test_visualizations/")
    print("   - test_convenience_vis/")
    print("   - test_config_vis/")
    
    print("\n💡 使用提示:")
    print("1. 在 CTDetDataset.__getitem__ 中调用 visualize_training_sample()")
    print("2. 设置 --enable_visualization 启用可视化")
    print("3. 使用 --visualization_sample_rate 控制采样率")
    print("4. 检查逻辑坐标是否正确标注在图像上")


if __name__ == "__main__":
    main()
