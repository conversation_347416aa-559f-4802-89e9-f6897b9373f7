#!/usr/bin/env python3
"""
测试语法修复是否成功
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root / "src"))

def test_import():
    """测试导入是否成功"""
    print("🧪 Testing import...")
    
    try:
        from lib.utils.data_visualizer import (
            LORETSRDataVisualizer, 
            visualize_if_enabled, 
            debug_ret_dict_structure,
            GlobalVisualizationController
        )
        print("✅ Import successful!")
        return True
    except SyntaxError as e:
        print(f"❌ Syntax error: {e}")
        return False
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error: {e}")
        return False


def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 Testing basic functionality...")
    
    try:
        from lib.utils.data_visualizer import GlobalVisualizationController
        
        # 测试控制器创建
        controller = GlobalVisualizationController()
        print(f"✅ Controller created: {type(controller)}")
        
        # 测试配置
        class MockOpt:
            def __init__(self):
                self.enable_visualization = True
                self.visualization_sample_rate = 0.5
        
        opt = MockOpt()
        controller.configure(opt)
        print(f"✅ Controller configured: enabled={controller.enabled}")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🔧 LORE-TSR Syntax Fix Test")
    print("=" * 40)
    
    tests = [
        ("Import test", test_import),
        ("Basic functionality", test_basic_functionality)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            print(f"✅ {test_name} passed")
            passed += 1
        else:
            print(f"❌ {test_name} failed")
    
    # 总结
    print(f"\n{'='*40}")
    print(f"📊 Test results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! Syntax fix successful!")
        print("\n💡 You can now run your training command:")
        print("python main.py ctdet_mid --enable_visualization ...")
    else:
        print("⚠️ Some tests failed, please check error messages above")


if __name__ == "__main__":
    main()
