#!/usr/bin/env python3
"""
性能监控功能测试脚本
验证缓存性能监控不会干扰正常训练流程，日志级别合理
"""

import sys
import os
import tempfile
import numpy as np
import logging
from PIL import Image

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def create_test_image(width=128, height=128):
    """创建测试图像文件"""
    img_array = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
    img = Image.fromarray(img_array)
    
    temp_file = tempfile.NamedTemporaryFile(suffix='.jpg', delete=False)
    img.save(temp_file.name)
    return temp_file.name

def test_performance_monitoring():
    """测试性能监控功能"""
    try:
        print("=== 性能监控功能测试 ===")
        
        # 创建测试图像
        test_img_path = create_test_image()
        print(f"创建测试图像: {test_img_path}")
        
        # 导入必要模块
        from lib.opts import opts
        from lib.datasets.sample.ctdet import CTDetDataset
        
        # 测试1: 监控方法存在性
        print("\n--- 测试1: 监控方法存在性 ---")
        dataset = CTDetDataset()
        
        assert hasattr(dataset, 'print_cache_stats'), "print_cache_stats方法不存在"
        assert hasattr(dataset, 'get_cache_summary'), "get_cache_summary方法不存在"
        assert hasattr(dataset, 'get_cache_memory_info'), "get_cache_memory_info方法不存在"
        print("✅ 所有监控方法存在")
        
        # 测试2: 缓存禁用时的监控行为
        print("\n--- 测试2: 缓存禁用时的监控行为 ---")
        opt_disabled = opts().parse(['ctdet'])
        opt_disabled.enable_data_cache = False
        dataset_disabled = CTDetDataset()
        dataset_disabled.opt = opt_disabled
        
        # 调用监控方法，应该不产生错误
        dataset_disabled.print_cache_stats()  # 应该静默返回
        summary = dataset_disabled.get_cache_summary()
        memory_info = dataset_disabled.get_cache_memory_info()
        
        assert summary is None, "缓存禁用时摘要应该为None"
        assert memory_info is None, "缓存禁用时内存信息应该为None"
        print("✅ 缓存禁用时监控行为正常")
        
        # 测试3: 缓存启用时的监控行为
        print("\n--- 测试3: 缓存启用时的监控行为 ---")
        opt_enabled = opts().parse(['ctdet', '--enable_data_cache', '--cache_size', '50', '--image_cache_memory_mb', '128'])
        dataset_enabled = CTDetDataset()
        dataset_enabled.opt = opt_enabled
        
        # 加载一些图像以产生统计数据
        img1 = dataset_enabled._get_cached_image(test_img_path)
        img2 = dataset_enabled._get_cached_image(test_img_path)  # 缓存命中
        
        assert img1 is not None, "图像加载失败"
        assert img2 is not None, "缓存命中加载失败"
        
        # 测试监控方法
        dataset_enabled.print_cache_stats()  # 应该输出DEBUG级别日志
        summary = dataset_enabled.get_cache_summary()
        memory_info = dataset_enabled.get_cache_memory_info()  # 默认不包含系统信息
        memory_info_full = dataset_enabled.get_cache_memory_info(include_system_info=True)  # 包含系统信息

        assert summary is not None, "缓存启用时摘要不应该为None"
        assert isinstance(summary, str), f"摘要应该是字符串: {type(summary)}"
        assert "命中率" in summary, f"摘要应该包含命中率: {summary}"

        assert memory_info is not None, "缓存启用时内存信息不应该为None"
        assert isinstance(memory_info, dict), f"内存信息应该是字典: {type(memory_info)}"
        assert 'cache_memory_mb' in memory_info, "内存信息应该包含cache_memory_mb"

        assert memory_info_full is not None, "完整内存信息不应该为None"
        assert 'system_total_gb' in memory_info_full, "完整内存信息应该包含系统信息"

        print(f"✅ 缓存摘要: {summary}")
        print(f"✅ 内存信息: {memory_info}")
        print(f"✅ 完整内存信息: {memory_info_full}")
        
        # 测试4: 日志级别验证
        print("\n--- 测试4: 日志级别验证 ---")
        
        # 设置日志级别为INFO，验证DEBUG日志不会输出
        logging.getLogger().setLevel(logging.INFO)
        
        # 调用print_cache_stats，应该不会有输出（因为使用DEBUG级别）
        print("调用print_cache_stats（应该无DEBUG输出）:")
        dataset_enabled.print_cache_stats()
        print("✅ DEBUG级别日志正确设置，不干扰训练")
        
        # 测试5: 性能影响验证
        print("\n--- 测试5: 性能影响验证 ---")
        import time
        
        # 测试监控方法的性能开销
        start_time = time.time()
        for _ in range(100):
            dataset_enabled.get_cache_summary()
        summary_time = time.time() - start_time
        
        start_time = time.time()
        for _ in range(100):
            dataset_enabled.get_cache_memory_info()
        memory_time = time.time() - start_time
        
        # 监控方法应该非常快（每次调用<1ms）
        assert summary_time < 0.1, f"get_cache_summary性能开销过大: {summary_time:.3f}s"
        assert memory_time < 0.1, f"get_cache_memory_info性能开销过大: {memory_time:.3f}s"
        
        print(f"✅ 性能开销验证通过 - 摘要: {summary_time*10:.2f}ms/100次, 内存: {memory_time*10:.2f}ms/100次")
        
        # 清理测试文件
        os.unlink(test_img_path)
        
        print("\n🎉 所有性能监控测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 性能监控测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_performance_monitoring()
    sys.exit(0 if success else 1)
