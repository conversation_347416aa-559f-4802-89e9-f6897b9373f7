# src/lib/utils/image_cache.py
"""
图像缓存模块 - LORE-TSR数据加载性能优化
提供线程安全的图像LRU缓存功能，支持内存管理和性能统计
"""

import os
import time
import threading
import numpy as np
import torchvision.io as tvio
from collections import OrderedDict
from typing import Optional, Dict, Any
from .logger_config import LoggerConfig


class ImageCache:
    """
    线程安全的图像LRU缓存类
    
    支持内存管理、性能统计和错误处理，与项目现有缓存架构保持一致
    """
    
    def __init__(self, max_memory_mb: int = 2048, max_items: int = 1000, logger=None):
        """
        初始化图像缓存
        
        Args:
            max_memory_mb (int): 最大内存使用量(MB)，默认2048MB
            max_items (int): 最大缓存项目数，默认1000
            logger: 日志记录器，如果为None则创建新的
        """
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.max_items = max_items
        self.logger = logger if logger is not None else LoggerConfig.setup_logger("ImageCache")
        
        # 线程安全的缓存存储
        self._cache = OrderedDict()
        self._lock = threading.RLock()
        
        # 内存和性能统计
        self._current_memory = 0
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_load_time': 0.0,
            'total_cache_time': 0.0
        }
        
        self.logger.info(f"图像缓存初始化完成 - 最大内存: {max_memory_mb}MB, 最大项目数: {max_items}")
    
    def get_image(self, img_path: str) -> Optional[np.ndarray]:
        """
        获取图像，优先从缓存读取
        
        Args:
            img_path (str): 图像文件路径
            
        Returns:
            Optional[np.ndarray]: 图像数组(HWC格式)，加载失败时返回None
        """
        with self._lock:
            # 检查缓存命中
            if img_path in self._cache:
                start_time = time.time()
                # 移动到末尾（LRU更新）
                img = self._cache.pop(img_path)
                self._cache[img_path] = img
                
                cache_time = time.time() - start_time
                self._stats['hits'] += 1
                self._stats['total_cache_time'] += cache_time
                
                self.logger.debug(f"缓存命中: {os.path.basename(img_path)} (加载耗时: {cache_time:.3f}s)")
                return img.copy()  # 返回副本避免修改原始缓存
            
            # 缓存未命中，从磁盘加载
            start_time = time.time()
            img = self._load_image_from_disk(img_path)
            load_time = time.time() - start_time
            
            self._stats['misses'] += 1
            self._stats['total_load_time'] += load_time
            
            if img is not None:
                self._add_to_cache(img_path, img)
                self.logger.debug(f"缓存保存: {os.path.basename(img_path)} (解析耗时: {load_time:.3f}s)")
            
            return img
    
    def _load_image_from_disk(self, img_path: str) -> Optional[np.ndarray]:
        """
        从磁盘加载图像，使用torchvision.io保持兼容性
        
        Args:
            img_path (str): 图像文件路径
            
        Returns:
            Optional[np.ndarray]: 图像数组(HWC格式)，加载失败时返回None
        """
        try:
            # 使用torchvision.io.read_image加载图像
            img_tensor = tvio.read_image(img_path, mode=tvio.ImageReadMode.RGB)
            # 转换为HWC格式的numpy数组，保持与现有代码兼容
            img = img_tensor.permute(1, 2, 0).numpy()
            return img
        except Exception as e:
            self.logger.warning(f"图像加载失败: {img_path}, 错误: {e}")
            return None
    
    def _add_to_cache(self, img_path: str, img: np.ndarray):
        """
        添加图像到缓存，管理内存限制
        
        Args:
            img_path (str): 图像文件路径
            img (np.ndarray): 图像数组
        """
        img_memory = img.nbytes
        
        # 检查是否超过单个图像内存限制（避免过大图像）
        if img_memory > self.max_memory_bytes * 0.1:  # 单个图像不超过总内存的10%
            self.logger.debug(f"图像过大，跳过缓存: {os.path.basename(img_path)} ({img_memory / (1024*1024):.1f}MB)")
            return
        
        # 确保有足够空间
        while (self._current_memory + img_memory > self.max_memory_bytes or 
               len(self._cache) >= self.max_items):
            if not self._cache:
                break
            self._evict_oldest()
        
        # 添加到缓存
        self._cache[img_path] = img.copy()
        self._current_memory += img_memory
    
    def _evict_oldest(self):
        """移除最旧的缓存项"""
        if self._cache:
            oldest_path, oldest_img = self._cache.popitem(last=False)
            self._current_memory -= oldest_img.nbytes
            self._stats['evictions'] += 1
            self.logger.debug(f"缓存清理: {os.path.basename(oldest_path)}")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = (self._stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            avg_load_time = (self._stats['total_load_time'] / self._stats['misses']) if self._stats['misses'] > 0 else 0
            avg_cache_time = (self._stats['total_cache_time'] / self._stats['hits']) if self._stats['hits'] > 0 else 0
            
            return {
                'cache_enabled': True,
                'cache_size': len(self._cache),
                'memory_usage_mb': round(self._current_memory / (1024 * 1024), 2),
                'max_memory_mb': round(self.max_memory_bytes / (1024 * 1024), 2),
                'cache_hits': self._stats['hits'],
                'cache_misses': self._stats['misses'],
                'evictions': self._stats['evictions'],
                'total_requests': total_requests,
                'hit_rate_percent': round(hit_rate, 2),
                'avg_load_time_ms': round(avg_load_time * 1000, 2),
                'avg_cache_time_ms': round(avg_cache_time * 1000, 2),
                'speedup_factor': round(avg_load_time / avg_cache_time, 2) if avg_cache_time > 0 else 0,
                'time_saved_ms': round((avg_load_time - avg_cache_time) * self._stats['hits'] * 1000, 2) if avg_load_time > avg_cache_time else 0
            }
    
    def print_stats(self):
        """打印缓存统计信息"""
        stats = self.get_stats()
        
        self.logger.info("=== 图像缓存统计 ===")
        self.logger.info(f"缓存大小: {stats['cache_size']} 项")
        self.logger.info(f"内存使用: {stats['memory_usage_mb']:.1f}MB / {stats['max_memory_mb']:.1f}MB")
        self.logger.info(f"缓存命中: {stats['cache_hits']}")
        self.logger.info(f"缓存未命中: {stats['cache_misses']}")
        self.logger.info(f"缓存清理: {stats['evictions']}")
        self.logger.info(f"命中率: {stats['hit_rate_percent']}%")
        self.logger.info(f"平均加载时间: {stats['avg_load_time_ms']}ms")
        self.logger.info(f"平均缓存时间: {stats['avg_cache_time_ms']}ms")
        if stats['speedup_factor'] > 0:
            self.logger.info(f"加速比: {stats['speedup_factor']}x")
        if stats['time_saved_ms'] > 0:
            self.logger.info(f"总节省时间: {stats['time_saved_ms']}ms")
    
    def clear_cache(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._current_memory = 0
            self.logger.info("图像缓存已清空")

    def preload_images(self, image_paths: list, max_preload: int = None):
        """
        预加载图像到缓存（预热缓存）

        Args:
            image_paths (list): 图像路径列表
            max_preload (int): 最大预加载数量，None表示不限制
        """
        if max_preload is None:
            max_preload = self.max_items

        preload_count = min(len(image_paths), max_preload)
        self.logger.info(f"开始预热缓存，预加载 {preload_count} 张图像...")

        success_count = 0
        for i, img_path in enumerate(image_paths[:preload_count]):
            try:
                img = self.get_image(img_path)
                if img is not None:
                    success_count += 1

                if (i + 1) % 100 == 0:
                    self.logger.debug(f"预加载进度: {i+1}/{preload_count}")

            except Exception as e:
                self.logger.warning(f"预加载图像失败: {img_path}, 错误: {e}")

        stats = self.get_stats()
        self.logger.info(f"缓存预热完成: 成功加载 {success_count}/{preload_count} 张图像")
        self.logger.info(f"缓存状态: {stats['cache_size']} 项, {stats['memory_usage_mb']:.1f}MB")
    
    def get_memory_info(self) -> Dict[str, Any]:
        """
        获取内存使用信息
        
        Returns:
            Dict[str, Any]: 内存使用信息
        """
        try:
            import psutil
            memory = psutil.virtual_memory()
            return {
                'system_total_gb': round(memory.total / (1024**3), 2),
                'system_available_gb': round(memory.available / (1024**3), 2),
                'system_used_percent': memory.percent,
                'cache_memory_mb': round(self._current_memory / (1024**2), 2),
                'cache_memory_percent': round(self._current_memory / memory.total * 100, 2)
            }
        except ImportError:
            self.logger.warning("psutil未安装，无法获取系统内存信息")
            return {
                'cache_memory_mb': round(self._current_memory / (1024**2), 2)
            }
