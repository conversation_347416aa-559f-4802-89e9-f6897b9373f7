#!/usr/bin/env python3
"""
JSON解析缓存机制测试脚本

用于验证TableLabelMe数据集的JSON解析缓存功能是否正常工作。
"""

import sys
import os
import time
import argparse

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from lib.opts import opts
from lib.datasets.dataset_factory import get_dataset


def test_cache_performance():
    """测试缓存性能"""
    print("=== JSON解析缓存性能测试 ===")
    
    # 创建测试配置
    opt = opts().parse(['ctdet', '--dataset', 'table_labelmev2', '--dataset_name', 'TableLabelMe'])
    
    # 启用缓存
    opt.enable_data_cache = True
    opt.cache_size = 1000
    
    print(f"缓存配置: enable_data_cache={opt.enable_data_cache}, cache_size={opt.cache_size}")
    
    try:
        # 第一次加载（缓存未命中）
        print("\n--- 第一次加载（预期缓存未命中） ---")
        start_time = time.time()
        
        # 获取数据集类
        Dataset = get_dataset(opt.dataset, 'ctdet', getattr(opt, 'config_data', None))
        
        # 创建数据集实例
        dataset = Dataset(opt, 'train')
        
        first_load_time = time.time() - start_time
        print(f"第一次加载耗时: {first_load_time:.2f}秒")
        
        # 显示缓存统计
        dataset.print_cache_stats()
        
        # 第二次加载（缓存命中）
        print("\n--- 第二次加载（预期缓存命中） ---")
        start_time = time.time()
        
        # 重新创建数据集实例
        dataset2 = Dataset(opt, 'train')
        
        second_load_time = time.time() - start_time
        print(f"第二次加载耗时: {second_load_time:.2f}秒")
        
        # 显示缓存统计
        dataset2.print_cache_stats()
        
        # 计算性能提升
        if first_load_time > 0:
            speedup = first_load_time / second_load_time if second_load_time > 0 else float('inf')
            improvement = ((first_load_time - second_load_time) / first_load_time * 100) if first_load_time > 0 else 0
            
            print(f"\n=== 性能提升分析 ===")
            print(f"加速比: {speedup:.2f}x")
            print(f"时间减少: {improvement:.1f}%")
            
            if improvement > 50:
                print("✅ 缓存机制工作正常，性能提升显著")
            elif improvement > 20:
                print("⚠️ 缓存机制工作，但性能提升有限")
            else:
                print("❌ 缓存机制可能存在问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_cache_disabled():
    """测试缓存禁用模式"""
    print("\n=== 缓存禁用模式测试 ===")
    
    # 创建测试配置
    opt = opts().parse(['ctdet', '--dataset', 'table_labelmev2', '--dataset_name', 'TableLabelMe'])
    
    # 禁用缓存
    opt.enable_data_cache = False
    
    print(f"缓存配置: enable_data_cache={opt.enable_data_cache}")
    
    try:
        start_time = time.time()
        
        # 获取数据集类
        Dataset = get_dataset(opt.dataset, 'ctdet', getattr(opt, 'config_data', None))
        
        # 创建数据集实例
        dataset = Dataset(opt, 'train')
        
        load_time = time.time() - start_time
        print(f"禁用缓存加载耗时: {load_time:.2f}秒")
        
        # 显示缓存统计
        dataset.print_cache_stats()
        
        print("✅ 缓存禁用模式工作正常")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    parser = argparse.ArgumentParser(description='JSON解析缓存测试')
    parser.add_argument('--test-disabled', action='store_true', help='测试缓存禁用模式')
    parser.add_argument('--test-performance', action='store_true', help='测试缓存性能')
    args = parser.parse_args()
    
    success = True
    
    if args.test_disabled or not (args.test_performance):
        success &= test_cache_disabled()
    
    if args.test_performance or not (args.test_disabled):
        success &= test_cache_performance()
    
    if success:
        print("\n🎉 所有测试通过！JSON解析缓存机制工作正常。")
        return 0
    else:
        print("\n❌ 测试失败！请检查缓存机制实现。")
        return 1


if __name__ == '__main__':
    sys.exit(main())
