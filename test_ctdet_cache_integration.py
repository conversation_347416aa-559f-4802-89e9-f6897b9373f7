#!/usr/bin/env python3
"""
CTDetDataset缓存集成测试脚本
验证延迟初始化和缓存方法是否正常工作
"""

import sys
import os
import tempfile
import numpy as np
from PIL import Image

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def create_test_image(width=100, height=100):
    """创建测试图像文件"""
    # 创建随机图像
    img_array = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
    img = Image.fromarray(img_array)
    
    # 保存到临时文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.jpg', delete=False)
    img.save(temp_file.name)
    return temp_file.name

def test_ctdet_cache_integration():
    """测试CTDetDataset缓存集成"""
    try:
        from lib.opts import opts
        from lib.datasets.sample.ctdet import CTDetDataset
        
        print("=== CTDetDataset缓存集成测试 ===")
        
        # 创建测试图像
        test_img_path = create_test_image()
        print(f"创建测试图像: {test_img_path}")
        
        # 测试1: 缓存禁用模式
        print("\n--- 测试1: 缓存禁用模式 ---")
        opt_disabled = opts().parse(['ctdet'])
        opt_disabled.enable_data_cache = False
        
        dataset_disabled = CTDetDataset()
        dataset_disabled.opt = opt_disabled
        
        # 测试延迟初始化
        img1 = dataset_disabled._get_cached_image(test_img_path)
        assert img1 is not None, "缓存禁用模式图像加载失败"
        assert hasattr(dataset_disabled, '_image_cache'), "延迟初始化失败"
        assert dataset_disabled._image_cache is None, "缓存禁用模式应该设置为None"
        print(f"✅ 缓存禁用模式正常 - 图像尺寸: {img1.shape}")
        
        # 测试2: 缓存启用模式
        print("\n--- 测试2: 缓存启用模式 ---")
        opt_enabled = opts().parse(['ctdet', '--enable_data_cache', '--cache_size', '100', '--image_cache_memory_mb', '512'])
        
        dataset_enabled = CTDetDataset()
        dataset_enabled.opt = opt_enabled
        
        # 测试延迟初始化
        img2 = dataset_enabled._get_cached_image(test_img_path)
        assert img2 is not None, "缓存启用模式图像加载失败"
        assert hasattr(dataset_enabled, '_image_cache'), "延迟初始化失败"
        assert dataset_enabled._image_cache is not None, "缓存启用模式应该创建缓存实例"
        print(f"✅ 缓存启用模式正常 - 图像尺寸: {img2.shape}")
        
        # 测试3: 缓存命中
        print("\n--- 测试3: 缓存命中测试 ---")
        img3 = dataset_enabled._get_cached_image(test_img_path)  # 第二次加载，应该命中缓存
        assert img3 is not None, "缓存命中加载失败"
        assert np.array_equal(img2, img3), "缓存图像与原图像不一致"
        
        # 获取缓存统计
        stats = dataset_enabled._image_cache.get_stats()
        print(f"缓存统计: 命中={stats['cache_hits']}, 未命中={stats['cache_misses']}, 命中率={stats['hit_rate_percent']}%")
        assert stats['cache_hits'] >= 1, "缓存命中次数应该大于等于1"
        print("✅ 缓存命中测试通过")
        
        # 测试4: 图像格式兼容性
        print("\n--- 测试4: 图像格式兼容性 ---")
        assert img1.dtype == np.uint8, f"图像数据类型错误: {img1.dtype}"
        assert len(img1.shape) == 3, f"图像维度错误: {img1.shape}"
        assert img1.shape[2] == 3, f"图像通道数错误: {img1.shape[2]}"
        print(f"✅ 图像格式兼容性正常 - 数据类型: {img1.dtype}, 形状: {img1.shape}")
        
        # 清理测试文件
        os.unlink(test_img_path)
        
        print("\n🎉 所有CTDetDataset缓存集成测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ CTDetDataset缓存集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_ctdet_cache_integration()
    sys.exit(0 if success else 1)
