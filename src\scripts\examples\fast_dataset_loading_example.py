#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-28
# <AUTHOR> LORE-TSR Team
# @FileName: fast_dataset_loading_example.py

"""
快速数据集加载示例

展示如何使用预处理索引缓存机制实现快速训练启动
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from lib.opts import opts
from lib.datasets.dataset.table_labelmev2 import Table


def demonstrate_fast_loading():
    """演示快速加载功能"""
    print("=== LORE-TSR 快速数据集加载演示 ===")
    print("任务1：预处理索引缓存机制实现")
    print()
    
    # 创建配置
    opt = opts().init()
    
    # 启用缓存（关键配置）
    opt.enable_data_cache = True
    opt.cache_size = 15000
    
    print("配置信息:")
    print(f"  - 数据缓存: {'启用' if opt.enable_data_cache else '禁用'}")
    print(f"  - 缓存大小: {opt.cache_size}")
    print(f"  - 保存目录: {getattr(opt, 'save_dir', './cache')}")
    print()
    
    # 测试不同分割的加载
    splits = ['train']  # 可以扩展为 ['train', 'val', 'test']
    
    for split in splits:
        print(f"=== 加载 {split.upper()} 数据集 ===")
        
        # 第一次加载（可能需要几十分钟）
        print("第一次加载（完整处理）...")
        start_time = time.time()
        
        try:
            dataset = Table(opt, split)
            load_time = time.time() - start_time
            
            print(f"✓ 加载完成")
            print(f"  - 耗时: {load_time:.2f}秒")
            print(f"  - 样本数: {len(dataset.images)}")
            print(f"  - 文件对: {len(dataset.file_index)}")
            
            # 显示统计信息
            if hasattr(dataset, 'scan_statistics'):
                stats = dataset.scan_statistics
                print(f"  - 扫描统计: {stats.get('valid_pairs', 0)}个有效对")
            
            if hasattr(dataset, 'filter_statistics'):
                stats = dataset.filter_statistics
                print(f"  - 质量筛选: {stats.get('valid_samples', 0)}个有效样本")
            
            # 缓存统计
            cache_stats = dataset.dataset_cache.get_cache_stats()
            if cache_stats.get('cache_enabled', False):
                print(f"  - 缓存状态: 已保存到磁盘")
                print(f"  - 缓存目录: {cache_stats.get('cache_dir', 'N/A')}")
            
            print()
            
            # 第二次加载（应该很快）
            print("第二次加载（从缓存）...")
            start_time = time.time()
            
            dataset2 = Table(opt, split)
            load_time2 = time.time() - start_time
            
            print(f"✓ 快速加载完成")
            print(f"  - 耗时: {load_time2:.2f}秒")
            print(f"  - 样本数: {len(dataset2.images)}")
            
            # 性能对比
            if load_time > 0:
                speedup = load_time / load_time2
                print(f"  - 性能提升: {speedup:.1f}倍")
                print(f"  - 时间节省: {load_time - load_time2:.2f}秒")
                
                if load_time2 < 10:
                    print(f"  ✓ 达成目标: 加载时间 < 10秒")
                else:
                    print(f"  ⚠ 需要优化: 加载时间 >= 10秒")
            
            print()
            
        except Exception as e:
            print(f"✗ 加载失败: {e}")
            print()
            continue
    
    print("=== 演示完成 ===")
    print()
    print("使用建议:")
    print("1. 首次运行时会进行完整的文件扫描和质量过滤")
    print("2. 后续运行将从缓存快速加载，大幅减少启动时间")
    print("3. 数据目录发生变化时，缓存会自动失效并重新生成")
    print("4. 可以通过 --enable_data_cache 控制缓存开关")


def show_cache_management():
    """展示缓存管理功能"""
    print("\n=== 缓存管理功能 ===")
    
    opt = opts().init()
    opt.enable_data_cache = True
    
    try:
        # 创建数据集实例
        dataset = Table(opt, 'train')
        
        # 显示缓存统计
        print("缓存统计信息:")
        dataset.dataset_cache.print_cache_stats()
        
        # 缓存管理操作
        print("\n缓存管理操作:")
        print("- 清理特定分割缓存: dataset.dataset_cache.clear_cache('train')")
        print("- 清理所有缓存: dataset.dataset_cache.clear_cache()")
        print("- 获取缓存统计: dataset.dataset_cache.get_cache_stats()")
        
    except Exception as e:
        print(f"缓存管理演示失败: {e}")


if __name__ == '__main__':
    demonstrate_fast_loading()
    show_cache_management()
