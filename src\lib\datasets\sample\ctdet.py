from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import torch.utils.data as data
import numpy as np
import torch
import json
import cv2
import os
from lib.utils.image import flip, color_aug
from lib.utils.image import get_affine_transform, affine_transform, get_affine_transform_upper_left
from lib.utils.post_process import ctdet_4ps_post_process
from lib.utils.image import gaussian_radius, draw_umich_gaussian, draw_umich_gaussian_wh, draw_msra_gaussian
from lib.utils.image import draw_dense_reg
from lib.utils.adjacency import adjacency, h_adjacency, v_adjacency, same_col, same_row
import math
import time
import random
import imgaug.augmenters as iaa
import time
from lib.utils.image_cache import ImageCache
from lib.utils.logger_config import LoggerConfig

from lib.utils.data_visualizer import visualize_if_enabled

class CTDetDataset(data.Dataset):
  def _init_image_cache(self):
    """
    延迟初始化图像缓存

    根据配置参数决定是否启用图像缓存，确保与现有架构兼容
    """
    if getattr(self.opt, 'enable_data_cache', False):
      # 创建日志器
      logger = LoggerConfig.setup_logger("ImageCache")

      # 创建ImageCache实例
      self._image_cache = ImageCache(
        max_memory_mb=getattr(self.opt, 'image_cache_memory_mb', 2048),
        max_items=getattr(self.opt, 'cache_size', 1000),
        logger=logger
      )
      # 只在缓存启用时显示INFO级别日志，避免干扰训练
      logger.debug(f"图像缓存已启用 - 内存限制: {getattr(self.opt, 'image_cache_memory_mb', 2048)}MB, 项目数: {getattr(self.opt, 'cache_size', 1000)}")
    else:
      self._image_cache = None
      # 缓存禁用时使用DEBUG级别，避免干扰训练日志
      logger = LoggerConfig.setup_logger("ImageCache")
      logger.debug("图像缓存未启用，使用直接加载模式")

  def _get_cached_image(self, img_path):
    """
    获取图像，优先从缓存读取

    Args:
        img_path (str): 图像文件路径

    Returns:
        np.ndarray: 图像数组(HWC格式)，与现有torchvision.io加载方式兼容
    """
    # 检查缓存是否已初始化（延迟初始化）
    if not hasattr(self, '_image_cache'):
      self._init_image_cache()

    # 如果缓存启用，使用缓存加载
    if self._image_cache is not None:
      return self._image_cache.get_image(img_path)
    else:
      # 缓存禁用，直接使用torchvision.io加载（保持兼容性）
      import torchvision.io as tvio
      try:
        img_tensor = tvio.read_image(img_path, mode=tvio.ImageReadMode.RGB)
        img = img_tensor.permute(1, 2, 0).numpy()  # 转换为HWC格式
        return img
      except Exception as e:
        # 错误处理，使用日志记录
        logger = LoggerConfig.setup_logger("ImageCache")
        logger.warning(f"图像加载失败: {img_path}, 错误: {e}")
        return None

  def print_cache_stats(self):
    """
    打印缓存统计信息

    使用DEBUG级别日志，不干扰正常训练流程
    """
    # 检查缓存是否已初始化
    if not hasattr(self, '_image_cache'):
      return

    if self._image_cache is None:
      # 使用DEBUG级别，避免干扰训练日志
      logger = LoggerConfig.setup_logger("ImageCache")
      logger.debug("图像缓存未启用")
      return

    # 获取统计信息
    stats = self._image_cache.get_stats()
    logger = LoggerConfig.setup_logger("ImageCache")

    # 使用DEBUG级别输出详细统计，避免干扰训练
    logger.debug("=== 图像缓存统计 ===")
    logger.debug(f"缓存大小: {stats['cache_size']} 项")
    logger.debug(f"内存使用: {stats['memory_usage_mb']:.1f}MB / {stats['max_memory_mb']:.1f}MB")
    logger.debug(f"缓存命中: {stats['cache_hits']}")
    logger.debug(f"缓存未命中: {stats['cache_misses']}")
    logger.debug(f"命中率: {stats['hit_rate_percent']}%")
    logger.debug(f"平均加载时间: {stats['avg_load_time_ms']}ms")
    logger.debug(f"平均缓存时间: {stats['avg_cache_time_ms']}ms")
    if stats['speedup_factor'] > 0:
      logger.debug(f"加速比: {stats['speedup_factor']}x")
    if stats['time_saved_ms'] > 0:
      logger.debug(f"总节省时间: {stats['time_saved_ms']}ms")

  def get_cache_summary(self):
    """
    获取缓存摘要信息（简化版本，适合在训练过程中调用）

    Returns:
        str: 缓存摘要字符串，如果缓存未启用则返回None
    """
    if not hasattr(self, '_image_cache') or self._image_cache is None:
      return None

    stats = self._image_cache.get_stats()
    if stats['total_requests'] == 0:
      return "缓存: 0次请求"

    return f"缓存: {stats['hit_rate_percent']:.1f}%命中率, {stats['memory_usage_mb']:.0f}MB"

  def get_cache_memory_info(self, include_system_info=False):
    """
    获取缓存内存使用信息

    Args:
        include_system_info (bool): 是否包含系统内存信息（较慢，默认False）

    Returns:
        dict: 内存使用信息，如果缓存未启用则返回None
    """
    if not hasattr(self, '_image_cache') or self._image_cache is None:
      return None

    if include_system_info:
      return self._image_cache.get_memory_info()
    else:
      # 只返回缓存内存信息，避免系统调用开销
      stats = self._image_cache.get_stats()
      return {
        'cache_memory_mb': stats['memory_usage_mb'],
        'max_memory_mb': stats['max_memory_mb']
      }

  def _coco_box_to_bbox(self, box):
    bbox = np.array([box[0], box[1], box[0] + box[2], box[1] + box[3]],
                    dtype=np.float32)
    return bbox

  def _get_border(self, border, size):
    i = 1
    while size - border // i <= border // i:
        i *= 2
    return border // i

  def _get_border_upper_left(self, border, size):
    i = 1
    while size/2 - border // i <= border // i:
        i *= 2
    return border // i

  def _get_radius(self,r,w,h):
    if w > h:
        k = float(w)/float(h)
    else:
        k = float(h)/float(w)
    ratio = k**0.5
    if w>h:
        r_w = r*ratio
        r_h = r
    else:
        r_h = r*ratio
        r_w = r
    return int(r_w),int(r_h)

  def color(self,image,p,magnitude):
    if np.random.randint(0,10) > p*10:
      return image
    gray_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    bgr_img = cv2.cvtColor(gray_img, cv2.COLOR_GRAY2BGR)
    img_float,bgr_img_float = img.astype(float), bgr_img.astype(float)
    diff = img_float - bgr_img_float
    diff = diff*magnitude
    diff_img_ = diff + bgr_img_float
    diff_img_ = diff_img_.astype(np.uint8)
    diff_img_ = np.array(diff_img_)
    diff_img_ = np.clip(diff_img_,0,255)
    diff_img_ = cv2.cvtColor(diff_img_,cv2.COLOR_BGR2RGB)
    diff_img_ = cv2.cvtColor(diff_img_,cv2.COLOR_RGB2BGR)
    return diff_img_

  def rotate(self,p,magnitude):
    if np.random.randint(0,10) > p*10:
      return 0
    rot = np.random.randint(magnitude[0],magnitude[1])
    return rot

  def hisEqulColor(self,img):
    (b, g, r) = cv2.split(img)
    bH = cv2.equalizeHist(b)
    gH = cv2.equalizeHist(g)
    rH = cv2.equalizeHist(r)
    result = cv2.merge((bH, gH, rH))
    return img

  def _judge(self,box):
    countx = len(list(set([box[0],box[2],box[4],box[6]]))) 
    county = len(list(set([box[1],box[3],box[5],box[7]]))) 
    if countx<2 or county<2:
        return False
    
    return True

  def _get_Center(self, point):
    x1 = point[0]
    y1 = point[1]
    x3 = point[2]
    y3 = point[3]
    x2 = point[4]
    y2 = point[5]
    x4 = point[6]
    y4 = point[7]
    w1 = math.sqrt((x1-x3)*(x1-x3)+(y1-y3)*(y1-y3))
    w2 = math.sqrt((x2-x4)*(x2-x4)+(y2-y4)*(y2-y4))
    h1 = math.sqrt((x1-x4)*(x1-x4)+(y1-y4)*(y1-y4))
    h2 = math.sqrt((x2-x3)*(x2-x3)+(y2-y3)*(y2-y3))
    nw = min(w1,w2)
    nh = min(h1,h2)
    x_dev = x4*y2-x4*y1-x3*y2+x3*y1-x2*y4+x2*y3+x1*y4-x1*y3
    y_dev = y4*x2-y4*x1-y3*x2+x1*y3-y2*x4+y2*x3+y1*x4-y1*x3
    c_x = 0
    c_y = 0
    if x_dev != 0:
      c_x = (y3*x4*x2-y4*x3*x2-y3*x4*x1+y4*x3*x1-y1*x2*x4+y2*x1*x4+y1*x2*x3-y2*x1*x3)/x_dev
    if y_dev != 0:
      c_y = (-y3*x4*y2+y4*x3*y2+y3*x4*y1-y4*x3*y1+y1*x2*y4-y1*x2*y3-y2*x1*y4+y2*x1*y3)/y_dev
    return nw,nh,c_x,c_y

  def _rank(self,bbox,cter,file_name):
    init_bbox = bbox
    #bbox = list(map(float,bbox))
    continue_sign = False
    bbox = [bbox[0:2],bbox[2:4],bbox[4:6],bbox[6:8]]
    bbox_= np.array(bbox) - np.array(cter)
    i,box_y,sign= 0,[],'LT'
    choice = []
    for box in bbox_:
        if box[0]<0 and box[1]<0:
            box_y.append(box)
            choice.append(i)
        i = i + 1
    if len(choice)==0: 
        i,box_y,sign = 0,[],'RT'
        for box in bbox_:
            if box[0]>0 and box[1]<0:  
                box_y.append(box)
                choice.append(i)
            i = i + 1
    if sign=='LT':
        ylist = np.array(box_y)[:,1]
        #index = list(ylist).index(max(ylist))  
        index = list(ylist).index(min(ylist))  
    elif sign=='RT':
        try:
            xlist = np.array(box_y)[:,0]
        except Exception as e: 
            print("center:",cter,"box:",init_bbox,"box_y:",box_y)
            return True,bbox
        index = list(xlist).index(min(xlist))  
    
    index = choice[index]
    p = []
    for i in range(4):
        if i + index < 4:
            p.append(bbox[index+i])
        else:
            p.append(bbox[index+i-4])
    return continue_sign,[p[0][0],p[0][1],p[1][0],p[1][1],p[2][0],p[2][1],p[3][0],p[3][1]]

  def __getitem__(self, index):
    img_id = self.images[index]
    file_name = self.coco.loadImgs(ids=[img_id])[0]['file_name']


    if self.opt.dataset_name == 'ICDAR19':
      if self.split == 'train':
        img_path = os.path.join(self.img_dir, 'train_images' ,file_name)
      else:
        img_path = os.path.join(self.img_dir, 'test_images' ,file_name)
    else:
      img_path = os.path.join(self.img_dir, file_name)
    ann_ids = self.coco.getAnnIds(imgIds=[img_id])
    anns = self.coco.loadAnns(ids=ann_ids)
    num_objs = min(len(anns), self.max_objs)


    num_cors = self.max_cors
    if self.opt.dataset_name == 'TG24K':
      img_path = img_path.replace('.jpg', '_org.png')
    elif self.opt.dataset_name == 'SciTSR':
      img_path = img_path.replace('.jpg', '.png')
    elif self.opt.dataset_name == 'PTN':
      img_path = img_path.replace('.jpg', '.png')
    elif self.opt.dataset_name == 'bankdata_june':
      img_path = img_path[:-4]
   
    # 使用缓存机制加载图像
    img = self._get_cached_image(img_path)

    # 错误处理：如果图像加载失败，跳过到下一个样本
    if img is None:
      from lib.utils.logger_config import LoggerConfig
      logger = LoggerConfig.setup_logger("CTDetDataset")
      logger.warning(f'图像加载失败: {img_path}')
      return self.__getitem__((index + 1) % len(self.images))
    img_size = img.shape

    height, width = img.shape[0], img.shape[1]

    if self.opt.upper_left:
      c = np.array([0, 0], dtype=np.float32)
    else:
      c = np.array([img.shape[1] / 2., img.shape[0] / 2.], dtype=np.float32)

    if self.opt.keep_res:
      input_h = (height | self.opt.pad)# + 1
      input_w = (width | self.opt.pad)# + 1
      s = np.array([input_w, input_h], dtype=np.float32)
    else:
      s = max(img.shape[0], img.shape[1]) * 1.0
      input_h, input_w = self.opt.input_h, self.opt.input_w
   
    
    flipped = False
    if self.split == 'train':
      if not self.opt.not_rand_crop:
        if self.opt.upper_left:
          c = np.array([0, 0], dtype=np.float32)
        else:
          s = s * np.random.choice(np.arange(0.6, 1.4, 0.1))
          w_border = self._get_border(128, img.shape[1])
          h_border = self._get_border(128, img.shape[0])
          c[0] = np.random.randint(low=w_border, high=img.shape[1] - w_border)
          c[1] = np.random.randint(low=h_border, high=img.shape[0] - h_border)

      else:
        sf = self.opt.scale
        cf = self.opt.shift
        c[0] += s * np.clip(np.random.randn()*cf, -2*cf, 2*cf)
        c[1] += s * np.clip(np.random.randn()*cf, -2*cf, 2*cf)
        s = s * np.clip(np.random.randn()*sf + 1, 1 - sf, 1 + sf)
    
    rot = 0
    if self.opt.rotate==1:
      print('----rotate----')
      rot = np.random.randint(-15,15) 

    output_h = input_h // self.opt.down_ratio
    output_w = input_w // self.opt.down_ratio

    # 安全索引计算函数（Bug修复 - 防止整数溢出）
    def safe_index_calc(y, x, width):
        """
        安全计算二维坐标到一维索引的转换，防止整数溢出

        Args:
            y: y坐标
            x: x坐标
            width: 图像宽度

        Returns:
            安全的索引值，限制在int64范围内
        """
        try:
            # 确保坐标在合理范围内
            y_safe = max(0, min(int(y), output_h - 1))
            x_safe = max(0, min(int(x), output_w - 1))
            width_safe = int(width)

            # 计算索引，检查溢出
            index = y_safe * width_safe + x_safe

            # 检查是否超出int64范围
            MAX_INT64 = 9223372036854775807
            if index > MAX_INT64:
                print(f"[ctdet警告] 🚨 索引计算溢出: y={y}, x={x}, width={width}, index={index}")
                # 使用安全的替代值
                index = y_safe * 1000 + x_safe  # 使用较小的宽度
                print(f"[ctdet修复] 🔧 使用安全索引: {index}")

            return int(index)

        except Exception as e:
            print(f"[ctdet错误] ❌ 索引计算失败: y={y}, x={x}, width={width}, error={e}")
            return 0

    if self.opt.upper_left:
      trans_input = get_affine_transform_upper_left(c, s, rot, [input_w, input_h])
      trans_output = get_affine_transform_upper_left(c, s, rot, [output_w, output_h])
      trans_output_mk = get_affine_transform_upper_left(c, s, rot, [output_w, output_h])
    else:
     
      trans_input = get_affine_transform(c, s, rot, [input_w, input_h])
      trans_output = get_affine_transform(c, s, rot, [output_w, output_h])
      trans_output_mk = get_affine_transform(c, s, rot, [output_w, output_h])
      
    num_classes = self.num_classes
    
    hm = np.zeros((num_classes, output_h, output_w), dtype=np.float32)
    wh = np.zeros((self.max_objs, 8), dtype=np.float32)
    reg = np.zeros((self.max_objs*5, 2), dtype=np.float32)
    st = np.zeros((self.max_cors, 8), dtype=np.float32)
    hm_ctxy = np.zeros((self.max_objs, 2), dtype=np.float32)
    hm_ind = np.zeros((self.max_objs), dtype=np.int64)
    hm_mask = np.zeros((self.max_objs), dtype=np.uint8)
    mk_ind = np.zeros((self.max_cors), dtype=np.int64)
    mk_mask = np.zeros((self.max_cors), dtype=np.uint8)
    reg_ind = np.zeros((self.max_objs*5), dtype=np.int64)
    reg_mask = np.zeros((self.max_objs*5), dtype=np.uint8)
    ctr_cro_ind = np.zeros((self.max_objs*4), dtype=np.int64)
    log_ax = np.zeros((self.max_objs, 4), dtype=np.float32)
    cc_match = np.zeros((self.max_objs, 4), dtype=np.int64)
    h_pair_ind = np.zeros((self.max_pairs), dtype=np.int64)
    v_pair_ind = np.zeros((self.max_pairs), dtype=np.int64)
    draw_gaussian = draw_msra_gaussian if self.opt.mse_loss else \
                    draw_umich_gaussian
    gt_det = []
    corList = []
    point = []
    pair_mark = 0
    inp = cv2.warpAffine(img, trans_input, (input_w, input_h),flags=cv2.INTER_LINEAR)
    
    for k in range(num_objs):
      ann = anns[k]
      
      seg_mask = ann['segmentation'][0] #[[351.0, 73.0, 172.0, 70.0, 174.0, 127.0, 351.0, 129.0, 351.0, 73.0]]
      x1,y1 = seg_mask[0],seg_mask[1]
      x2,y2 = seg_mask[2],seg_mask[3]
      x3,y3 = seg_mask[4],seg_mask[5]
      x4,y4 = seg_mask[6],seg_mask[7]
   
      CorNer = np.array([x1,y1,x2,y2,x3,y3,x4,y4])
      boxes = [[CorNer[0],CorNer[1]],[CorNer[2],CorNer[3]],\
               [CorNer[4],CorNer[5]],[CorNer[6],CorNer[7]]]
      cls_id = int(self.cat_ids[ann['category_id']])

      if flipped:
       
        CorNer[[0,2,4,6]] = width - CorNer[[2,0,6,4]] - 1

      CorNer[0:2] = affine_transform(CorNer[0:2], trans_output_mk)
      CorNer[2:4] = affine_transform(CorNer[2:4], trans_output_mk)
      CorNer[4:6] = affine_transform(CorNer[4:6], trans_output_mk)
      CorNer[6:8] = affine_transform(CorNer[6:8], trans_output_mk)
      CorNer[[0,2,4,6]] = np.clip(CorNer[[0,2,4,6]], 0, output_w - 1)
      CorNer[[1,3,5,7]] = np.clip(CorNer[[1,3,5,7]], 0, output_h - 1)
      if not self._judge(CorNer):
          continue
 
      maxx = max([CorNer[2*I] for I in range(0,4)])
      minx = min([CorNer[2*I] for I in range(0,4)])
      maxy = max([CorNer[2*I+1] for I in range(0,4)])
      miny = min([CorNer[2*I+1] for I in range(0,4)])
      h, w = maxy-miny, maxx-minx #bbox[3] - bbox[1], bbox[2] - bbox[0]
      if h > 0 and w > 0:
       
        radius = gaussian_radius((math.ceil(h), math.ceil(w)))
        radius = max(0, int(radius))
        radius = self.opt.hm_gauss if self.opt.mse_loss else radius

        ct = np.array([(maxx+minx)/2.0,(maxy+miny)/2.0], dtype=np.float32)

        ct_int = ct.astype(np.int32)
       
        draw_gaussian(hm[cls_id], ct_int, radius)

        for i in range(4):
          Cor = np.array([CorNer[2*i],CorNer[2*i+1]], dtype=np.float32)
          Cor_int = Cor.astype(np.int32)
          Cor_key = str(Cor_int[0])+"_"+str(Cor_int[1])
          if Cor_key not in corList:
            
            corNum = len(corList)
            
            corList.append(Cor_key)
            reg[self.max_objs+corNum] = np.array([abs(Cor[0]-Cor_int[0]),abs(Cor[1]-Cor_int[1])])
            # 安全索引计算（Bug修复 - 防止整数溢出）
            mk_ind[corNum] = safe_index_calc(Cor_int[1], Cor_int[0], output_w)
            cc_match[k][i] = mk_ind[corNum]
            reg_ind[self.max_objs+corNum] = safe_index_calc(Cor_int[1], Cor_int[0], output_w)
            mk_mask[corNum] = 1
            reg_mask[self.max_objs+corNum] = 1
            draw_gaussian(hm[num_classes-1], Cor_int, 2)
            st[corNum][i*2:(i+1)*2] = np.array([Cor[0]-ct[0],Cor[1]-ct[1]])
            ctr_cro_ind[4*k+i] = corNum*4 + i
        
          else:
            index_of_key = corList.index(Cor_key)
            cc_match[k][i] = mk_ind[index_of_key]
            st[index_of_key][i*2:(i+1)*2] = np.array([Cor[0]-ct[0],Cor[1]-ct[1]])
            ctr_cro_ind[4*k+i] = index_of_key*4 + i
            
        wh[k] = ct[0] - 1. * CorNer[0], ct[1] - 1. * CorNer[1], \
                ct[0] - 1. * CorNer[2], ct[1] - 1. * CorNer[3], \
                ct[0] - 1. * CorNer[4], ct[1] - 1. * CorNer[5], \
                ct[0] - 1. * CorNer[6], ct[1] - 1. * CorNer[7]
        
        # 安全索引计算（Bug修复 - 防止整数溢出）
        hm_ind[k] = safe_index_calc(ct_int[1], ct_int[0], output_w)
        hm_mask[k] = 1
        reg_ind[k] = safe_index_calc(ct_int[1], ct_int[0], output_w)
        reg_mask[k] = 1
        reg[k] = ct - ct_int
        hm_ctxy[k] = ct[0],ct[1]

        log_ax[k] = ann['logic_axis'][0][0], ann['logic_axis'][0][1], ann['logic_axis'][0][2], ann['logic_axis'][0][3]

     
        gt_det.append([ct[0] - 1. * CorNer[0], ct[1] - 1. * CorNer[1],
                       ct[0] - 1. * CorNer[2], ct[1] - 1. * CorNer[3],
                       ct[0] - 1. * CorNer[4], ct[1] - 1. * CorNer[5], 
                       ct[0] - 1. * CorNer[6], ct[1] - 1. * CorNer[7], 1, cls_id])
        
    hm_mask_v = hm_mask.reshape(1, hm_mask.shape[0])
  
    inp = (inp.astype(np.float32) / 255.)
    if self.split == 'train' and not self.opt.no_color_aug:
      color_aug(self._data_rng, inp, self._eig_val, self._eig_vec)
    

    inp = (inp - self.mean) / self.std
    inp = inp.transpose(2, 0, 1)

    ret = {'input': inp, 'hm': hm, 'hm_ind':hm_ind, 'hm_mask':hm_mask, 'mk_ind':mk_ind, 'mk_mask':mk_mask, 'reg':reg,'reg_ind':reg_ind,'reg_mask': reg_mask, \
           'wh': wh,'st':st, 'ctr_cro_ind':ctr_cro_ind, 'cc_match': cc_match, 'hm_ctxy':hm_ctxy, 'logic': log_ax, 'h_pair_ind': h_pair_ind, 'v_pair_ind': v_pair_ind}

    if self.opt.dense_wh:
      hm_a = hm.max(axis=0, keepdims=True)
      dense_wh_mask = np.concatenate([hm_a, hm_a], axis=0)
      ret.update({'dense_wh': dense_wh, 'dense_wh_mask': dense_wh_mask})
      del ret['wh']
    elif self.opt.cat_spec_wh:
      ret.update({'cat_spec_wh': cat_spec_wh, 'cat_spec_mask': cat_spec_mask})
      del ret['wh']
    if self.opt.reg_offset:
      ret.update({'reg': reg})
    if self.opt.debug > 0 or not self.split == 'train':
      gt_det = np.array(gt_det, dtype=np.float32) if len(gt_det) > 0 else \
               np.zeros((1, 10), dtype=np.float32)
      meta = {'c': c, 's': s, 'rot':rot, 'gt_det': gt_det, 'img_id': img_id}
      ret['meta'] = meta

    # 可视化功能
    # visualize_if_enabled(ret, index, self.opt)
    return ret
