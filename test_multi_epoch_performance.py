#!/usr/bin/env python3
"""
多epoch训练性能测试

模拟真实的多epoch训练场景，验证图像缓存在实际训练中的效果
"""

import sys
import os
import time
import tempfile
import numpy as np
from PIL import Image
from typing import List

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def create_dataset_simulation(image_count: int = 1000) -> List[str]:
    """创建模拟数据集"""
    image_paths = []
    temp_dir = tempfile.mkdtemp(prefix='multi_epoch_test_')
    
    print(f"创建 {image_count} 张测试图像...")
    for i in range(image_count):
        # 创建512x512的测试图像
        img_array = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
        img = Image.fromarray(img_array)
        
        img_path = os.path.join(temp_dir, f'img_{i:04d}.jpg')
        img.save(img_path, quality=95)
        image_paths.append(img_path)
    
    return image_paths, temp_dir

def simulate_epoch(dataset, image_paths: List[str], epoch_num: int) -> float:
    """模拟一个epoch的训练"""
    import random
    
    # 模拟DataLoader的shuffle
    shuffled_paths = image_paths.copy()
    random.shuffle(shuffled_paths)
    
    start_time = time.time()
    
    for i, img_path in enumerate(shuffled_paths):
        # 模拟数据加载
        img = dataset._get_cached_image(img_path)
        if img is None:
            continue
        
        # 模拟简单的数据处理
        img_processed = img.astype(np.float32) / 255.0
        
        # 每100张图像显示进度
        if (i + 1) % 100 == 0:
            print(f"  Epoch {epoch_num}: 处理了 {i+1}/{len(shuffled_paths)} 张图像")
    
    epoch_time = time.time() - start_time
    return epoch_time

def test_multi_epoch_performance():
    """测试多epoch性能"""
    try:
        from lib.opts import opts
        from lib.datasets.sample.ctdet import CTDetDataset
        
        # 创建测试数据集
        image_count = 1000  # 模拟1000张图像的小数据集
        cache_size = 300    # 缓存30%的图像
        
        image_paths, temp_dir = create_dataset_simulation(image_count)
        
        print(f"\n=== 多epoch性能测试 ===")
        print(f"数据集大小: {image_count} 张图像")
        print(f"缓存大小: {cache_size} 张图像 ({cache_size/image_count*100:.1f}%)")
        
        # 测试1: 无缓存
        print(f"\n--- 无缓存测试 ---")
        opt_no_cache = opts().parse(['ctdet'])
        opt_no_cache.enable_data_cache = False
        
        dataset_no_cache = CTDetDataset()
        dataset_no_cache.opt = opt_no_cache
        
        no_cache_times = []
        for epoch in range(1, 4):  # 测试3个epoch
            epoch_time = simulate_epoch(dataset_no_cache, image_paths, epoch)
            no_cache_times.append(epoch_time)
            print(f"  Epoch {epoch}: {epoch_time:.2f}s")
        
        avg_no_cache = np.mean(no_cache_times)
        print(f"平均每epoch时间: {avg_no_cache:.2f}s")
        
        # 测试2: 有缓存
        print(f"\n--- 有缓存测试 (cache_size={cache_size}) ---")
        opt_cache = opts().parse(['ctdet', '--enable_data_cache', '--cache_size', str(cache_size), '--image_cache_memory_mb', str(cache_size * 2)])
        
        dataset_cache = CTDetDataset()
        dataset_cache.opt = opt_cache
        
        cache_times = []
        for epoch in range(1, 4):  # 测试3个epoch
            epoch_time = simulate_epoch(dataset_cache, image_paths, epoch)
            cache_times.append(epoch_time)
            
            # 获取缓存统计
            if hasattr(dataset_cache, '_image_cache') and dataset_cache._image_cache:
                stats = dataset_cache._image_cache.get_stats()
                hit_rate = stats['hit_rate_percent']
                print(f"  Epoch {epoch}: {epoch_time:.2f}s (命中率: {hit_rate:.1f}%)")
            else:
                print(f"  Epoch {epoch}: {epoch_time:.2f}s")
        
        avg_cache = np.mean(cache_times)
        print(f"平均每epoch时间: {avg_cache:.2f}s")
        
        # 分析结果
        print(f"\n=== 性能分析结果 ===")
        improvement = ((avg_no_cache - avg_cache) / avg_no_cache * 100) if avg_no_cache > 0 else 0
        speedup = avg_no_cache / avg_cache if avg_cache > 0 else 1
        
        print(f"无缓存平均时间: {avg_no_cache:.2f}s")
        print(f"有缓存平均时间: {avg_cache:.2f}s")
        print(f"性能提升: {improvement:.1f}%")
        print(f"加速比: {speedup:.2f}x")
        
        # 获取最终缓存统计
        if hasattr(dataset_cache, '_image_cache') and dataset_cache._image_cache:
            final_stats = dataset_cache._image_cache.get_stats()
            print(f"最终缓存命中率: {final_stats['hit_rate_percent']:.1f}%")
            print(f"缓存大小: {final_stats['cache_size']} 项")
            print(f"内存使用: {final_stats['memory_usage_mb']:.1f}MB")
        
        # 给出建议
        print(f"\n=== 优化建议 ===")
        if improvement > 30:
            print("✅ 缓存效果显著，建议在实际训练中启用")
        elif improvement > 15:
            print("⚠️ 缓存效果一般，可以考虑增大缓存大小")
        else:
            print("❌ 缓存效果有限，需要重新评估缓存策略")
        
        # 针对3万图像的建议
        print(f"\n针对您的3万图像数据集的建议:")
        optimal_cache_size = min(10000, image_count // 3)  # 建议缓存1/3的数据
        optimal_memory = optimal_cache_size * 3  # 假设每张图像3MB
        print(f"- 建议缓存大小: {optimal_cache_size} 张图像")
        print(f"- 建议内存配置: {optimal_memory}MB ({optimal_memory/1024:.1f}GB)")
        print(f"- 预期命中率: {optimal_cache_size/30000*100:.1f}% (在多epoch训练中)")
        
        # 清理
        import shutil
        shutil.rmtree(temp_dir)
        
        return improvement > 15
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 多epoch训练性能测试开始")
    success = test_multi_epoch_performance()
    
    if success:
        print("\n🎉 测试完成！缓存机制在多epoch训练中有效。")
    else:
        print("\n⚠️ 测试显示缓存效果有限，需要进一步优化。")
