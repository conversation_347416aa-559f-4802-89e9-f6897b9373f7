# LORE-TSR 数据可视化功能

## 概述

本可视化工具专为 LORE-TSR 项目设计，用于在训练前验证和检查数据质量。特别强调**逻辑坐标的可视化**，帮助开发者确保表格结构识别的训练数据正确性。

## 功能特点

### 🎯 核心功能
- **逻辑坐标可视化**: 将逻辑坐标直接绘制到原图上，清晰显示单元格的行列位置
- **边界框检测**: 可视化四边形单元格边界框
- **热力图分析**: 显示中心点和角点的热力图分布
- **角点关系**: 展示单元格与角点的对应关系
- **数据统计**: 提供详细的数据质量统计信息

### 📊 可视化内容
1. **输入图像**: 反标准化后的原始输入图像
2. **热力图**: 单元格中心点和角点的检测热力图
3. **边界框**: 四边形单元格边界框可视化
4. **逻辑坐标**: 在原图上标注每个单元格的逻辑位置 `(行,列)`
5. **角点关系**: 单元格中心与四个角点的连接关系
6. **综合视图**: 多种可视化结果的组合展示
7. **统计信息**: JSON格式的详细数据统计

## 安装和配置

### 1. 文件部署
将以下文件放入对应目录：
```
src/lib/utils/data_visualizer.py          # 核心可视化类
src/lib/utils/visualization_opts.py       # 配置参数定义
src/lib/utils/visualization_integration_example.py  # 集成示例
test_visualization.py                     # 测试脚本
```

### 2. 依赖安装
```bash
pip install matplotlib opencv-python seaborn numpy
```

### 3. 在 opts.py 中添加参数
在 `src/lib/opts.py` 的 `__init__` 方法中添加：
```python
from lib.utils.visualization_opts import add_visualization_args
add_visualization_args(self.parser)
```

## 使用方法

### 方法1: 在 CTDetDataset 中集成

在 `src/lib/datasets/sample/ctdet.py` 中修改：

```python
# 1. 在文件顶部添加导入
from lib.utils.data_visualizer import visualize_training_sample

# 2. 在 __init__ 方法中添加配置
def __init__(self, opt, split):
    # ... 原有代码 ...
    
    # 可视化配置
    self.enable_vis = getattr(opt, 'enable_visualization', False)
    self.vis_dir = getattr(opt, 'visualization_dir', 'debug_visualizations')
    self.vis_sample_rate = getattr(opt, 'visualization_sample_rate', 0.1)
    self.vis_counter = 0

# 3. 在 __getitem__ 方法的 return ret 之前添加
def __getitem__(self, index):
    # ... 原有的所有数据处理代码 ...
    
    # 构建返回字典
    ret = {'input': inp, 'hm': hm, 'hm_ind': hm_ind, ...}
    
    # 可视化功能（新增）
    if self.enable_vis and self._should_visualize():
        try:
            sample_id = f"sample_{index:06d}"
            visualize_training_sample(
                ret_dict=ret,
                sample_id=sample_id,
                save_dir=self.vis_dir,
                down_ratio=self.opt.down_ratio
            )
            print(f"✅ 已保存样本 {sample_id} 的可视化结果")
        except Exception as e:
            print(f"⚠️ 样本 {index} 可视化失败: {e}")
    
    return ret

# 4. 添加辅助方法
def _should_visualize(self) -> bool:
    """判断是否应该对当前样本进行可视化"""
    self.vis_counter += 1
    if self.vis_sample_rate <= 0.0:
        return False
    interval = max(1, int(1.0 / self.vis_sample_rate))
    return (self.vis_counter % interval) == 0
```

### 方法2: 训练时启用

```bash
# 启用可视化，10% 采样率
python main.py ctdet \
    --exp_id debug_with_visualization \
    --enable_visualization \
    --visualization_dir ./debug_vis \
    --visualization_sample_rate 0.1 \
    --visualization_level 2 \
    --debug 1

# 仅可视化逻辑坐标，5% 采样率
python main.py ctdet \
    --exp_id logic_check \
    --enable_visualization \
    --visualization_sample_rate 0.05 \
    --visualization_level 1 \
    --vis_show_merge_info
```

### 方法3: 独立使用

```python
from lib.utils.data_visualizer import visualize_training_sample

# 假设你有一个 ret_dict
ret_dict = {...}  # CTDetDataset.__getitem__ 的返回值

# 直接可视化
visualize_training_sample(
    ret_dict=ret_dict,
    sample_id="my_sample_001",
    save_dir="my_visualizations",
    down_ratio=4
)
```

## 配置参数

### 基础参数
- `--enable_visualization`: 启用可视化功能
- `--visualization_dir`: 保存目录 (默认: `debug_visualizations`)
- `--visualization_sample_rate`: 采样率 0.0-1.0 (默认: 0.1)
- `--visualization_level`: 详细程度 0-3 (默认: 1)

### 样式参数
- `--vis_logic_font_size`: 逻辑坐标字体大小 (默认: 10)
- `--vis_show_cell_index`: 显示单元格索引
- `--vis_show_merge_info`: 显示合并单元格信息
- `--vis_heatmap_alpha`: 热力图透明度 (默认: 0.5)
- `--vis_bbox_color`: 边界框颜色 (默认: red)

### 控制参数
- `--vis_max_samples_per_epoch`: 每epoch最大可视化样本数 (默认: 100)
- `--vis_only_first_epoch`: 仅在第一个epoch可视化

## 输出文件结构

```
debug_visualizations/
├── input/                          # 输入图像
│   └── sample_000001_input.jpg
├── heatmap/                        # 热力图
│   └── sample_000001_heatmaps.png
├── bbox/                           # 边界框
│   └── sample_000001_bbox.png
├── logic/                          # 逻辑坐标 (重点)
│   ├── sample_000001_logic.png     # 逻辑坐标可视化图
│   └── sample_000001_logic_table.json  # 逻辑坐标数据表
├── corners/                        # 角点关系
│   └── sample_000001_corners.png
├── combined/                       # 综合视图
│   └── sample_000001_combined.png
└── sample_000001_statistics.json  # 统计信息
```

## 逻辑坐标可视化说明

### 显示内容
- **彩色区域**: 单元格边界框，不同颜色区分不同单元格
- **圆点**: 单元格中心点
- **坐标标注**: `(行,列)` 格式显示逻辑位置
- **合并单元格**: 显示起始和结束坐标，如 `(0,0)→(1,2)`
- **索引编号**: 红色圆圈中的数字表示单元格索引

### 验证要点
1. **坐标正确性**: 检查逻辑坐标是否与实际表格结构一致
2. **合并单元格**: 验证跨行跨列的单元格标注是否正确
3. **边界对齐**: 确认边界框与实际单元格边界对齐
4. **索引连续性**: 检查单元格索引是否连续且合理

## 测试和验证

### 运行测试脚本
```bash
python test_visualization.py
```

### 检查测试结果
1. 查看生成的可视化文件
2. 验证逻辑坐标标注是否正确
3. 检查边界框是否合理
4. 确认统计信息的准确性

## 故障排除

### 常见问题

1. **导入错误**
   ```
   ImportError: No module named 'lib.utils.data_visualizer'
   ```
   **解决**: 确保文件路径正确，检查 Python 路径设置

2. **可视化文件未生成**
   - 检查 `enable_visualization` 是否为 True
   - 确认采样率设置合理 (> 0.0)
   - 查看控制台错误信息

3. **逻辑坐标显示异常**
   - 检查 `logic` 字段的数据格式
   - 确认 `hm_mask` 正确标记有效单元格
   - 验证 `hm_ctxy` 中心坐标的合理性

4. **内存占用过高**
   - 降低 `visualization_sample_rate`
   - 设置 `vis_max_samples_per_epoch`
   - 使用 `vis_only_first_epoch`

### 调试技巧

1. **启用详细日志**
   ```bash
   python main.py ctdet --debug 2 --enable_visualization
   ```

2. **单样本测试**
   ```python
   # 设置采样率为 1.0，只处理少量样本
   --visualization_sample_rate 1.0 --num_epochs 1
   ```

3. **检查数据统计**
   查看生成的 `*_statistics.json` 文件，了解数据分布

## 性能建议

- **训练时**: 使用较低的采样率 (0.01-0.1)
- **调试时**: 可以使用较高的采样率 (0.5-1.0)
- **生产环境**: 建议关闭可视化功能
- **存储空间**: 定期清理可视化文件，避免占用过多磁盘空间

## 扩展功能

可以根据需要扩展以下功能：
- 添加更多的可视化样式
- 支持视频格式输出
- 集成到 TensorBoard
- 添加交互式可视化界面
- 支持批量对比分析
