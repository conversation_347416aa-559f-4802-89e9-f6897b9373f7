#!/usr/bin/env python3
"""
图像缓存效果验证脚本 - LORE-TSR性能优化验证

专门用于验证图像缓存机制的效果，包括性能对比、内存使用监控、缓存命中率测试等。
确保缓存机制在实际训练场景中能达到预期效果，且不影响训练过程。

目标验证：
- 缓存命中率 > 70%
- 性能提升 > 50%
- 内存使用可控
- 训练流程不受影响
"""

import sys
import os
import time
import tempfile
import numpy as np
import argparse
import threading
from PIL import Image
from typing import List, Dict, Any

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

class CacheValidationReport:
    """缓存验证报告生成器"""
    
    def __init__(self):
        self.results = {}
        self.start_time = time.time()
    
    def add_result(self, test_name: str, success: bool, details: str, duration: float = 0):
        """添加测试结果"""
        self.results[test_name] = {
            'success': success,
            'details': details,
            'duration': duration,
            'timestamp': time.time() - self.start_time
        }
    
    def generate_report(self) -> str:
        """生成验证报告"""
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results.values() if r['success'])
        
        report = f"""
=== LORE-TSR 图像缓存效果验证报告 ===
测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
总测试数: {total_tests}
通过测试: {passed_tests}
成功率: {passed_tests/total_tests*100:.1f}%

"""
        
        for test_name, result in self.results.items():
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            report += f"{status} {test_name}\n"
            report += f"   详情: {result['details']}\n"
            if result['duration'] > 0:
                report += f"   耗时: {result['duration']:.3f}s\n"
            report += "\n"
        
        return report

def create_test_images(count: int = 20, width: int = 512, height: int = 512) -> List[str]:
    """创建测试图像文件"""
    image_paths = []
    temp_dir = tempfile.mkdtemp(prefix='lore_cache_test_')
    
    for i in range(count):
        # 创建不同的随机图像
        img_array = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
        img = Image.fromarray(img_array)
        
        img_path = os.path.join(temp_dir, f'test_image_{i:03d}.jpg')
        img.save(img_path, quality=95)
        image_paths.append(img_path)
    
    return image_paths, temp_dir

def cleanup_test_images(temp_dir: str):
    """清理测试图像"""
    import shutil
    try:
        shutil.rmtree(temp_dir)
    except Exception as e:
        print(f"清理测试图像失败: {e}")

def cache_performance_test(reporter: CacheValidationReport) -> bool:
    """缓存性能对比测试"""
    try:
        print("\n=== 缓存性能对比测试 ===")
        
        from lib.opts import opts
        from lib.datasets.sample.ctdet import CTDetDataset
        
        # 创建测试图像
        test_images, temp_dir = create_test_images(count=50)
        print(f"创建了 {len(test_images)} 个测试图像")
        
        # 测试1: 缓存禁用性能
        print("\n--- 测试缓存禁用性能 ---")
        opt_disabled = opts().parse(['ctdet'])
        opt_disabled.enable_data_cache = False
        
        dataset_disabled = CTDetDataset()
        dataset_disabled.opt = opt_disabled
        
        start_time = time.time()
        # 模拟更真实的训练场景：多个epoch的随机访问
        import random
        for epoch in range(5):  # 模拟5个epoch
            # 每个epoch随机打乱图像顺序（模拟DataLoader的shuffle）
            shuffled_images = test_images.copy()
            random.shuffle(shuffled_images)
            for img_path in shuffled_images:
                img = dataset_disabled._get_cached_image(img_path)
                if img is None:
                    raise Exception(f"图像加载失败: {img_path}")
        disabled_time = time.time() - start_time
        
        print(f"缓存禁用总耗时: {disabled_time:.3f}s")
        
        # 测试2: 缓存启用性能
        print("\n--- 测试缓存启用性能 ---")
        opt_enabled = opts().parse(['ctdet', '--enable_data_cache', '--cache_size', '100', '--image_cache_memory_mb', '512'])
        
        dataset_enabled = CTDetDataset()
        dataset_enabled.opt = opt_enabled
        
        start_time = time.time()
        # 模拟更真实的训练场景：多个epoch的随机访问
        import random
        for epoch in range(5):  # 模拟5个epoch
            # 每个epoch随机打乱图像顺序（模拟DataLoader的shuffle）
            shuffled_images = test_images.copy()
            random.shuffle(shuffled_images)
            for img_path in shuffled_images:
                img = dataset_enabled._get_cached_image(img_path)
                if img is None:
                    raise Exception(f"图像加载失败: {img_path}")
        enabled_time = time.time() - start_time
        
        print(f"缓存启用总耗时: {enabled_time:.3f}s")
        
        # 计算性能提升
        speedup = disabled_time / enabled_time if enabled_time > 0 else 0
        improvement = ((disabled_time - enabled_time) / disabled_time * 100) if disabled_time > 0 else 0
        
        print(f"性能提升: {improvement:.1f}%")
        print(f"加速比: {speedup:.2f}x")
        
        # 获取缓存统计
        stats = dataset_enabled._image_cache.get_stats()
        hit_rate = stats['hit_rate_percent']
        
        print(f"缓存命中率: {hit_rate:.1f}%")
        
        # 验证目标
        performance_target_met = improvement >= 50.0
        hit_rate_target_met = hit_rate >= 70.0
        
        success = performance_target_met and hit_rate_target_met
        details = f"性能提升: {improvement:.1f}% (目标≥50%), 命中率: {hit_rate:.1f}% (目标≥70%), 加速比: {speedup:.2f}x"
        
        reporter.add_result("缓存性能对比", success, details, disabled_time + enabled_time)
        
        # 清理
        cleanup_test_images(temp_dir)
        
        return success
        
    except Exception as e:
        reporter.add_result("缓存性能对比", False, f"测试失败: {e}")
        return False

def memory_usage_test(reporter: CacheValidationReport) -> bool:
    """内存使用测试"""
    try:
        print("\n=== 内存使用测试 ===")
        
        from lib.opts import opts
        from lib.datasets.sample.ctdet import CTDetDataset
        
        # 创建测试图像
        test_images, temp_dir = create_test_images(count=100, width=256, height=256)
        
        # 启用缓存，设置较小的内存限制
        opt = opts().parse(['ctdet', '--enable_data_cache', '--cache_size', '50', '--image_cache_memory_mb', '128'])
        
        dataset = CTDetDataset()
        dataset.opt = opt
        
        # 逐步加载图像，监控内存使用
        memory_usage = []
        for i, img_path in enumerate(test_images):
            img = dataset._get_cached_image(img_path)
            if img is None:
                raise Exception(f"图像加载失败: {img_path}")
            
            if i % 10 == 0:  # 每10张图像记录一次内存使用
                stats = dataset._image_cache.get_stats()
                memory_usage.append(stats['memory_usage_mb'])
                print(f"加载 {i+1} 张图像，内存使用: {stats['memory_usage_mb']:.1f}MB")
        
        # 验证内存限制
        final_stats = dataset._image_cache.get_stats()
        max_memory_used = max(memory_usage)
        memory_limit = 128  # MB
        
        memory_within_limit = max_memory_used <= memory_limit * 1.1  # 允许10%的误差
        cache_size_reasonable = final_stats['cache_size'] <= 50
        
        success = memory_within_limit and cache_size_reasonable
        details = f"最大内存使用: {max_memory_used:.1f}MB (限制: {memory_limit}MB), 缓存大小: {final_stats['cache_size']} 项"
        
        reporter.add_result("内存使用控制", success, details)
        
        # 清理
        cleanup_test_images(temp_dir)
        
        return success
        
    except Exception as e:
        reporter.add_result("内存使用控制", False, f"测试失败: {e}")
        return False

def stress_test(reporter: CacheValidationReport) -> bool:
    """压力测试"""
    try:
        print("\n=== 压力测试 ===")
        
        from lib.opts import opts
        from lib.datasets.sample.ctdet import CTDetDataset
        
        # 创建测试图像
        test_images, temp_dir = create_test_images(count=30)
        
        opt = opts().parse(['ctdet', '--enable_data_cache', '--cache_size', '20', '--image_cache_memory_mb', '256'])
        
        dataset = CTDetDataset()
        dataset.opt = opt
        
        # 多线程并发访问测试
        def worker_thread(thread_id: int, results: List):
            try:
                for i in range(10):  # 每个线程访问10次
                    img_path = test_images[i % len(test_images)]
                    img = dataset._get_cached_image(img_path)
                    if img is None:
                        results.append(f"线程{thread_id}: 图像加载失败")
                        return
                results.append(f"线程{thread_id}: 成功")
            except Exception as e:
                results.append(f"线程{thread_id}: 异常 - {e}")
        
        # 启动多个线程
        threads = []
        results = []
        thread_count = 4
        
        start_time = time.time()
        for i in range(thread_count):
            thread = threading.Thread(target=worker_thread, args=(i, results))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        stress_time = time.time() - start_time
        
        # 检查结果
        success_count = sum(1 for r in results if "成功" in r)
        all_success = success_count == thread_count
        
        # 获取最终统计
        final_stats = dataset._image_cache.get_stats()
        
        success = all_success and final_stats['total_requests'] > 0
        details = f"并发线程: {thread_count}, 成功: {success_count}, 总请求: {final_stats['total_requests']}, 耗时: {stress_time:.3f}s"
        
        reporter.add_result("并发压力测试", success, details, stress_time)
        
        # 清理
        cleanup_test_images(temp_dir)
        
        return success
        
    except Exception as e:
        reporter.add_result("并发压力测试", False, f"测试失败: {e}")
        return False

def training_compatibility_test(reporter: CacheValidationReport) -> bool:
    """训练兼容性测试"""
    try:
        print("\n=== 训练兼容性测试 ===")
        
        from lib.opts import opts
        from lib.datasets.sample.ctdet import CTDetDataset
        
        # 测试监控方法不影响训练
        opt = opts().parse(['ctdet', '--enable_data_cache'])
        dataset = CTDetDataset()
        dataset.opt = opt
        
        # 模拟训练过程中的监控调用
        start_time = time.time()
        
        for i in range(100):
            # 模拟训练过程中可能的监控调用
            summary = dataset.get_cache_summary()
            memory_info = dataset.get_cache_memory_info()
            
            # 这些调用应该非常快，不影响训练
            if i % 50 == 0:
                dataset.print_cache_stats()  # DEBUG级别，不应该有输出
        
        monitoring_time = time.time() - start_time
        
        # 验证监控开销
        avg_time_per_call = monitoring_time / 100
        acceptable_overhead = avg_time_per_call < 0.001  # 每次调用<1ms
        
        success = acceptable_overhead
        details = f"100次监控调用耗时: {monitoring_time:.3f}s, 平均每次: {avg_time_per_call*1000:.2f}ms"
        
        reporter.add_result("训练兼容性", success, details, monitoring_time)
        
        return success
        
    except Exception as e:
        reporter.add_result("训练兼容性", False, f"测试失败: {e}")
        return False

def main():
    """主测试函数"""
    parser = argparse.ArgumentParser(description='LORE-TSR图像缓存效果验证')
    parser.add_argument('--quick', action='store_true', help='快速测试模式')
    parser.add_argument('--report-file', type=str, help='保存报告到文件')
    args = parser.parse_args()
    
    print("🚀 LORE-TSR 图像缓存效果验证开始")
    print("=" * 50)
    
    reporter = CacheValidationReport()
    
    # 执行测试
    tests = [
        ("缓存性能对比测试", cache_performance_test),
        ("内存使用测试", memory_usage_test),
        ("训练兼容性测试", training_compatibility_test),
    ]
    
    if not args.quick:
        tests.append(("并发压力测试", stress_test))
    
    overall_success = True
    for test_name, test_func in tests:
        print(f"\n🔍 执行 {test_name}...")
        try:
            success = test_func(reporter)
            overall_success &= success
            status = "✅ 通过" if success else "❌ 失败"
            print(f"{status} {test_name}")
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
            overall_success = False
    
    # 生成报告
    report = reporter.generate_report()
    print("\n" + "=" * 50)
    print(report)
    
    # 保存报告到文件
    if args.report_file:
        with open(args.report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"📄 报告已保存到: {args.report_file}")
    
    # 总结
    if overall_success:
        print("🎉 所有测试通过！图像缓存机制工作正常，达到预期效果。")
        print("💡 建议：可以在训练中启用图像缓存以提升性能。")
        return 0
    else:
        print("⚠️ 部分测试失败！请检查缓存机制实现。")
        return 1

if __name__ == '__main__':
    sys.exit(main())
