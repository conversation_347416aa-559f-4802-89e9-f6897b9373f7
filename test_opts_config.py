#!/usr/bin/env python3
"""
配置参数测试脚本
验证image_cache_memory_mb参数是否正确添加和解析
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_image_cache_config():
    """测试图像缓存配置参数"""
    try:
        from lib.opts import opts
        
        print("=== 图像缓存配置参数测试 ===")
        
        # 测试默认参数
        print("\n--- 测试默认参数 ---")
        opt = opts().parse(['ctdet'])
        
        print(f"enable_data_cache: {getattr(opt, 'enable_data_cache', 'NOT_FOUND')}")
        print(f"cache_size: {getattr(opt, 'cache_size', 'NOT_FOUND')}")
        print(f"image_cache_memory_mb: {getattr(opt, 'image_cache_memory_mb', 'NOT_FOUND')}")
        
        # 验证默认值
        assert hasattr(opt, 'image_cache_memory_mb'), "image_cache_memory_mb参数未找到"
        assert opt.image_cache_memory_mb == 2048, f"默认值错误，期望2048，实际{opt.image_cache_memory_mb}"
        
        # 测试自定义参数
        print("\n--- 测试自定义参数 ---")
        opt_custom = opts().parse(['ctdet', '--enable_data_cache', '--cache_size', '500', '--image_cache_memory_mb', '1024'])
        
        print(f"enable_data_cache: {opt_custom.enable_data_cache}")
        print(f"cache_size: {opt_custom.cache_size}")
        print(f"image_cache_memory_mb: {opt_custom.image_cache_memory_mb}")
        
        # 验证自定义值
        assert opt_custom.enable_data_cache == True, "enable_data_cache设置失败"
        assert opt_custom.cache_size == 500, f"cache_size设置错误，期望500，实际{opt_custom.cache_size}"
        assert opt_custom.image_cache_memory_mb == 1024, f"image_cache_memory_mb设置错误，期望1024，实际{opt_custom.image_cache_memory_mb}"
        
        print("\n✅ 所有配置参数测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 配置参数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_image_cache_config()
    sys.exit(0 if success else 1)
