#!/usr/bin/env python3
"""
简单的缓存方法测试脚本
验证CTDetDataset中的缓存方法是否正确添加
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_cache_methods_exist():
    """测试缓存方法是否存在"""
    try:
        print("=== 缓存方法存在性测试 ===")
        
        # 导入CTDetDataset类
        from lib.datasets.sample.ctdet import CTDetDataset
        
        # 创建实例
        dataset = CTDetDataset()
        
        # 检查方法是否存在
        assert hasattr(dataset, '_init_image_cache'), "_init_image_cache方法不存在"
        assert hasattr(dataset, '_get_cached_image'), "_get_cached_image方法不存在"
        
        print("✅ _init_image_cache方法存在")
        print("✅ _get_cached_image方法存在")
        
        # 检查方法是否可调用
        assert callable(getattr(dataset, '_init_image_cache')), "_init_image_cache不可调用"
        assert callable(getattr(dataset, '_get_cached_image')), "_get_cached_image不可调用"
        
        print("✅ 缓存方法可调用")
        
        print("\n🎉 缓存方法存在性测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 缓存方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_imports():
    """测试导入是否正常"""
    try:
        print("\n=== 导入测试 ===")
        
        # 测试ImageCache导入
        from lib.utils.image_cache import ImageCache
        print("✅ ImageCache导入成功")
        
        # 测试LoggerConfig导入
        from lib.utils.logger_config import LoggerConfig
        print("✅ LoggerConfig导入成功")
        
        # 测试CTDetDataset导入
        from lib.datasets.sample.ctdet import CTDetDataset
        print("✅ CTDetDataset导入成功")
        
        print("\n🎉 所有导入测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success1 = test_imports()
    success2 = test_cache_methods_exist()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！CTDetDataset缓存集成成功！")
        sys.exit(0)
    else:
        print("\n❌ 测试失败！")
        sys.exit(1)
