"""
LORE-TSR 可视化相关配置参数
用于在 opts.py 中添加可视化功能的命令行参数
"""


def add_visualization_args(parser):
    """
    添加可视化相关的命令行参数
    
    Args:
        parser: argparse.ArgumentParser 实例
    
    使用方法：
        在 src/lib/opts.py 的 __init__ 方法中调用：
        from lib.utils.visualization_opts import add_visualization_args
        add_visualization_args(self.parser)
    """
    
    # 可视化功能开关
    parser.add_argument('--enable_visualization', action='store_true',
                       help='启用训练数据可视化功能，用于调试和验证数据质量')
    
    # 可视化保存目录
    parser.add_argument('--visualization_dir', type=str, default='debug_visualizations',
                       help='可视化结果保存目录路径')
    
    # 可视化采样率
    parser.add_argument('--visualization_sample_rate', type=float, default=0.1,
                       help='可视化采样率 (0.0-1.0)，控制多少比例的样本进行可视化。'
                            '例如：0.1 表示每10个样本可视化1个')
    
    # 可视化详细程度
    parser.add_argument('--visualization_level', type=int, default=1, choices=[0, 1, 2, 3],
                       help='可视化详细程度：'
                            '0=关闭, 1=基础(输入+逻辑坐标), 2=详细(+热力图+边界框), 3=完整(+角点+统计)')
    
    # 可视化图像格式
    parser.add_argument('--visualization_format', type=str, default='png', choices=['png', 'jpg'],
                       help='可视化图像保存格式')
    
    # 可视化图像质量/DPI
    parser.add_argument('--visualization_dpi', type=int, default=150,
                       help='可视化图像DPI，影响图像清晰度和文件大小')
    
    # 是否保存原始数据
    parser.add_argument('--save_visualization_data', action='store_true',
                       help='是否保存可视化的原始数据(JSON格式)，用于后续分析')
    
    # 可视化时的下采样比例覆盖
    parser.add_argument('--vis_down_ratio', type=int, default=None,
                       help='可视化时使用的下采样比例，如果不指定则使用模型的down_ratio')
    
    # 逻辑坐标可视化相关
    parser.add_argument('--vis_logic_font_size', type=int, default=10,
                       help='逻辑坐标标注的字体大小')
    
    parser.add_argument('--vis_show_cell_index', action='store_true',
                       help='是否在可视化中显示单元格索引编号')
    
    parser.add_argument('--vis_show_merge_info', action='store_true', default=True,
                       help='是否在可视化中显示合并单元格信息')
    
    # 热力图可视化相关
    parser.add_argument('--vis_heatmap_alpha', type=float, default=0.5,
                       help='热力图叠加时的透明度 (0.0-1.0)')
    
    parser.add_argument('--vis_heatmap_colormap', type=str, default='hot',
                       help='热力图颜色映射，可选：hot, viridis, plasma, inferno等')
    
    # 边界框可视化相关
    parser.add_argument('--vis_bbox_line_width', type=float, default=2.0,
                       help='边界框线条宽度')
    
    parser.add_argument('--vis_bbox_color', type=str, default='red',
                       help='边界框颜色')
    
    # 批量可视化控制
    parser.add_argument('--vis_max_samples_per_epoch', type=int, default=100,
                       help='每个epoch最多可视化的样本数量，防止生成过多文件')
    
    parser.add_argument('--vis_only_first_epoch', action='store_true',
                       help='是否只在第一个epoch进行可视化')
    
    # 可视化文件命名
    parser.add_argument('--vis_include_timestamp', action='store_true',
                       help='是否在可视化文件名中包含时间戳')
    
    parser.add_argument('--vis_include_epoch', action='store_true',
                       help='是否在可视化文件名中包含epoch信息')


def get_visualization_config(opt):
    """
    从配置对象中提取可视化相关配置
    
    Args:
        opt: 配置对象
        
    Returns:
        dict: 可视化配置字典
    """
    vis_config = {
        'enabled': getattr(opt, 'enable_visualization', False),
        'save_dir': getattr(opt, 'visualization_dir', 'debug_visualizations'),
        'sample_rate': getattr(opt, 'visualization_sample_rate', 0.1),
        'level': getattr(opt, 'visualization_level', 1),
        'format': getattr(opt, 'visualization_format', 'png'),
        'dpi': getattr(opt, 'visualization_dpi', 150),
        'save_data': getattr(opt, 'save_visualization_data', False),
        'down_ratio': getattr(opt, 'vis_down_ratio', None) or getattr(opt, 'down_ratio', 4),
        
        # 样式配置
        'logic_font_size': getattr(opt, 'vis_logic_font_size', 10),
        'show_cell_index': getattr(opt, 'vis_show_cell_index', False),
        'show_merge_info': getattr(opt, 'vis_show_merge_info', True),
        'heatmap_alpha': getattr(opt, 'vis_heatmap_alpha', 0.5),
        'heatmap_colormap': getattr(opt, 'vis_heatmap_colormap', 'hot'),
        'bbox_line_width': getattr(opt, 'vis_bbox_line_width', 2.0),
        'bbox_color': getattr(opt, 'vis_bbox_color', 'red'),
        
        # 控制配置
        'max_samples_per_epoch': getattr(opt, 'vis_max_samples_per_epoch', 100),
        'only_first_epoch': getattr(opt, 'vis_only_first_epoch', False),
        'include_timestamp': getattr(opt, 'vis_include_timestamp', False),
        'include_epoch': getattr(opt, 'vis_include_epoch', False),
    }
    
    return vis_config


def validate_visualization_config(vis_config):
    """
    验证可视化配置的有效性
    
    Args:
        vis_config: 可视化配置字典
        
    Returns:
        tuple: (is_valid, error_messages)
    """
    errors = []
    
    # 检查采样率
    if not 0.0 <= vis_config['sample_rate'] <= 1.0:
        errors.append(f"visualization_sample_rate 必须在 0.0-1.0 范围内，当前值: {vis_config['sample_rate']}")
    
    # 检查可视化级别
    if vis_config['level'] not in [0, 1, 2, 3]:
        errors.append(f"visualization_level 必须是 0, 1, 2, 3 中的一个，当前值: {vis_config['level']}")
    
    # 检查DPI
    if vis_config['dpi'] < 50 or vis_config['dpi'] > 600:
        errors.append(f"visualization_dpi 建议在 50-600 范围内，当前值: {vis_config['dpi']}")
    
    # 检查透明度
    if not 0.0 <= vis_config['heatmap_alpha'] <= 1.0:
        errors.append(f"vis_heatmap_alpha 必须在 0.0-1.0 范围内，当前值: {vis_config['heatmap_alpha']}")
    
    # 检查线条宽度
    if vis_config['bbox_line_width'] <= 0:
        errors.append(f"vis_bbox_line_width 必须大于 0，当前值: {vis_config['bbox_line_width']}")
    
    # 检查最大样本数
    if vis_config['max_samples_per_epoch'] <= 0:
        errors.append(f"vis_max_samples_per_epoch 必须大于 0，当前值: {vis_config['max_samples_per_epoch']}")
    
    return len(errors) == 0, errors


def print_visualization_config(vis_config):
    """
    打印可视化配置信息
    
    Args:
        vis_config: 可视化配置字典
    """
    print("\n🎨 可视化配置信息:")
    print("=" * 50)
    
    if not vis_config['enabled']:
        print("❌ 可视化功能已关闭")
        return
    
    print("✅ 可视化功能已启用")
    print(f"📁 保存目录: {vis_config['save_dir']}")
    print(f"📊 采样率: {vis_config['sample_rate']*100:.1f}%")
    print(f"📈 详细程度: {vis_config['level']} (0=关闭, 1=基础, 2=详细, 3=完整)")
    print(f"🖼️  图像格式: {vis_config['format'].upper()}")
    print(f"🔍 图像DPI: {vis_config['dpi']}")
    print(f"📏 下采样比例: {vis_config['down_ratio']}")
    
    if vis_config['level'] >= 2:
        print(f"🔥 热力图透明度: {vis_config['heatmap_alpha']}")
        print(f"🎨 热力图颜色: {vis_config['heatmap_colormap']}")
    
    if vis_config['level'] >= 1:
        print(f"📝 逻辑坐标字体: {vis_config['logic_font_size']}px")
        print(f"🔢 显示单元格索引: {'是' if vis_config['show_cell_index'] else '否'}")
        print(f"🔗 显示合并信息: {'是' if vis_config['show_merge_info'] else '否'}")
    
    print(f"⚡ 每epoch最大样本数: {vis_config['max_samples_per_epoch']}")
    print(f"🎯 仅第一epoch: {'是' if vis_config['only_first_epoch'] else '否'}")
    print("=" * 50)


# 使用示例
if __name__ == "__main__":
    import argparse
    
    # 创建解析器并添加可视化参数
    parser = argparse.ArgumentParser()
    add_visualization_args(parser)
    
    # 解析示例参数
    args = parser.parse_args([
        '--enable_visualization',
        '--visualization_sample_rate', '0.05',
        '--visualization_level', '2',
        '--vis_show_cell_index'
    ])
    
    # 获取配置
    vis_config = get_visualization_config(args)
    
    # 验证配置
    is_valid, errors = validate_visualization_config(vis_config)
    
    if is_valid:
        print_visualization_config(vis_config)
    else:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
