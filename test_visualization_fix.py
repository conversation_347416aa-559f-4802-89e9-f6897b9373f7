#!/usr/bin/env python3
"""
测试可视化修复是否有效
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root / "src"))

def test_visualization_controller():
    """测试可视化控制器的配置和功能"""
    print("🧪 测试可视化控制器...")
    
    try:
        from lib.utils.data_visualizer import GlobalVisualizationController, get_visualization_controller
        
        # 创建模拟的 opt 对象
        class MockOpt:
            def __init__(self):
                self.enable_visualization = True
                self.visualization_dir = "test_debug_vis"
                self.visualization_sample_rate = 0.5
                self.vis_max_samples_per_epoch = 100
                self.down_ratio = 4
        
        opt = MockOpt()
        
        # 测试控制器获取
        controller = get_visualization_controller()
        print(f"✅ 控制器创建成功: {type(controller)}")
        
        # 测试配置
        print(f"🔧 配置前 - configured: {getattr(controller, 'configured', False)}")
        controller.configure(opt)
        print(f"🔧 配置后 - configured: {getattr(controller, 'configured', False)}")
        
        # 测试 should_visualize
        print(f"\n🔍 测试 should_visualize 方法:")
        for i in range(5):
            result = controller.should_visualize()
            print(f"   第{i+1}次调用: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_visualize_if_enabled():
    """测试 visualize_if_enabled 函数"""
    print("\n🧪 测试 visualize_if_enabled 函数...")
    
    try:
        from lib.utils.data_visualizer import visualize_if_enabled
        import numpy as np
        
        # 创建模拟的 opt 对象
        class MockOpt:
            def __init__(self):
                self.enable_visualization = True
                self.visualization_dir = "test_debug_vis"
                self.visualization_sample_rate = 1.0  # 100% 采样率用于测试
                self.vis_max_samples_per_epoch = 100
                self.down_ratio = 4
        
        opt = MockOpt()
        
        # 创建模拟的 ret_dict
        ret_dict = {
            'input': np.random.randn(3, 512, 512).astype(np.float32),
            'hm': np.random.rand(2, 128, 128).astype(np.float32),
            'hm_ind': np.random.randint(0, 128*128, 50).astype(np.int64),
            'hm_mask': np.ones(50, dtype=np.uint8),
            'mk_ind': np.random.randint(0, 128*128, 200).astype(np.int64),
            'mk_mask': np.ones(200, dtype=np.uint8),
            'reg': np.random.randn(250, 2).astype(np.float32),
            'reg_ind': np.random.randint(0, 128*128, 250).astype(np.int64),
            'reg_mask': np.ones(250, dtype=np.uint8),
            'wh': np.random.randn(50, 8).astype(np.float32) * 20,
            'st': np.random.randn(200, 8).astype(np.float32) * 10,
            'ctr_cro_ind': np.random.randint(0, 200, 200).astype(np.int64),
            'cc_match': np.random.randint(0, 200, (50, 4)).astype(np.int64),
            'hm_ctxy': np.random.rand(50, 2).astype(np.float32) * 128,
            'logic': np.random.randint(0, 5, (50, 4)).astype(np.float32),
            'h_pair_ind': np.zeros(20, dtype=np.int64),
            'v_pair_ind': np.zeros(20, dtype=np.int64)
        }
        
        # 确保逻辑坐标合理
        for i in range(50):
            if ret_dict['hm_mask'][i] == 1:
                start_row = np.random.randint(0, 3)
                start_col = np.random.randint(0, 3)
                end_row = start_row + np.random.randint(0, 2)
                end_col = start_col + np.random.randint(0, 2)
                ret_dict['logic'][i] = [start_row, end_row, start_col, end_col]
        
        # 测试函数调用
        print(f"🎨 调用 visualize_if_enabled...")
        visualize_if_enabled(ret_dict, 0, opt)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_opts_integration():
    """测试 opts.py 集成"""
    print("\n🧪 测试 opts.py 集成...")
    
    try:
        sys.path.append(str(project_root / "src"))
        from opts import opts
        
        # 创建 opts 实例
        opt_parser = opts()
        
        # 解析测试参数
        test_args = [
            'ctdet',
            '--enable_visualization',
            '--visualization_sample_rate', '0.5',
            '--vis_show_merge_info'
        ]
        
        parsed_opt = opt_parser.parse(test_args)
        
        print(f"✅ 参数解析成功:")
        print(f"   - enable_visualization: {getattr(parsed_opt, 'enable_visualization', 'NOT_FOUND')}")
        print(f"   - visualization_sample_rate: {getattr(parsed_opt, 'visualization_sample_rate', 'NOT_FOUND')}")
        print(f"   - vis_show_merge_info: {getattr(parsed_opt, 'vis_show_merge_info', 'NOT_FOUND')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🔧 LORE-TSR 可视化修复验证测试")
    print("=" * 60)
    
    # 检查依赖
    try:
        import matplotlib
        import cv2
        import numpy as np
        print("✅ 依赖库检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖库: {e}")
        return
    
    # 运行测试
    tests = [
        ("可视化控制器", test_visualization_controller),
        ("visualize_if_enabled 函数", test_visualize_if_enabled),
        ("opts.py 集成", test_opts_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            print(f"✅ {test_name} 测试通过")
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
    
    # 总结
    print(f"\n{'='*60}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！可视化功能应该正常工作了")
        print("\n💡 使用建议:")
        print("1. 运行训练命令时确保包含 --enable_visualization")
        print("2. 设置合适的采样率，如 --visualization_sample_rate 0.1")
        print("3. 检查控制台输出中的可视化配置信息")
        print("4. 查看生成的可视化文件")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
    
    # 清理测试文件
    import shutil
    test_dir = Path("test_debug_vis")
    if test_dir.exists():
        shutil.rmtree(test_dir)
        print(f"🧹 清理测试目录: {test_dir}")


if __name__ == "__main__":
    main()
